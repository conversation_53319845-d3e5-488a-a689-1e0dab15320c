// components/plate-manager/index.js
import { apiCreateLicensePlate, apiDeleteLicensePlate, apiGetLicensePlateList } from '../../api/plate';

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    selectedPlate: {
      type: String,
      value: ''
    }
  },

  data: {
    plateList: [],
    newPlate: '',
    showAddPlatePopup: false,
    loading: false
  },

  lifetimes: {
    attached() {
      if (this.properties.visible) {
        this.fetchPlates();
      }
    }
  },

  observers: {
    'visible': function (visible) {
      console.log('plate-manager visible changed:', visible);
      if (visible) {
        this.fetchPlates();
      }
    }
  },

  methods: {
    // 获取车牌列表
    async fetchPlates() {
      try {
        this.setData({ loading: true });

        const res = await apiGetLicensePlateList();
        console.log('获取车牌列表API响应:', res);
        if (res && res.code === 0 && res.data) {
          // 处理车牌列表数据 - 新的API返回结构中，数据在data.results中
          const plates = res.data.results || res.data || [];

          if (Array.isArray(plates)) {
            console.log('处理车牌数据:', plates);
            // 提取车牌号列表
            const plateNumbers = [];
            let defaultPlate = '';

            // 遍历车牌列表，找出默认车牌
            plates.forEach(plate => {
              plateNumbers.push({
                id: plate.id,
                plateNo: plate.plateNo,
                isDefault: plate.isDefault && plate.isDefault.code === 'YES'
              });

              // 如果是默认车牌，记录下来
              if (plate.isDefault && plate.isDefault.code === 'YES') {
                defaultPlate = plate.plateNo;
              }
            });

            console.log('处理后的车牌列表:', plateNumbers);
            console.log('默认车牌:', defaultPlate);

            this.setData({
              plateList: plateNumbers,
              loading: false
            });

            // 如果没有选中的车牌，自动选择默认车牌
            if (!this.properties.selectedPlate && defaultPlate) {
              const defaultPlateObj = plateNumbers.find(p => p.plateNo === defaultPlate);
              if (defaultPlateObj) {
                console.log('自动选择默认车牌:', defaultPlateObj);
                this.triggerEvent('select', defaultPlateObj);
              }
            }
          } else {
            this.setData({ plateList: [], loading: false });
            this.showMessage('车牌列表数据格式错误');
          }
        } else {
          this.setData({ plateList: [], loading: false });
          this.showMessage('获取车牌列表失败');
        }
      } catch (error) {
        console.error('获取车牌列表错误:', error);
        this.setData({ plateList: [], loading: false });
        this.showMessage('获取车牌列表失败');
      }
    },

    // 选择车牌
    selectPlate(e) {
      const plate = e.currentTarget.dataset.plate;
      console.log('选择车牌:', plate);
      this.triggerEvent('select', plate);
      this.close();
    },

    // 添加车牌弹窗
    showAddPlatePopup() {
      this.setData({ showAddPlatePopup: true });
    },

    // 关闭添加车牌弹窗
    closeAddPlatePopup() {
      this.setData({
        showAddPlatePopup: false,
        newPlate: ''
      });
    },

    // 输入车牌号
    onPlateInput(e) {
      this.setData({
        newPlate: e.detail.value
      });
    },

    // 添加新车牌
    async addNewPlate() {
      const { newPlate, plateList } = this.data;
      this.closeAddPlatePopup()
      if (!newPlate) {
        this.showMessage('请输入车牌号');
        return;
      }

      // 简单的车牌号验证
      const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5,6}$/;
      if (!plateRegex.test(newPlate)) {
        this.showMessage('请输入正确的车牌号');
        return;
      }

      // 检查是否已存在
      const plateExists = plateList.some(plate => plate.plateNo === newPlate);
      if (plateExists) {
        this.showMessage('该车牌已存在');
        return;
      }

      try {
        this.setData({ loading: true });

        // 调用添加车牌API
        const res = await apiCreateLicensePlate({
          plateNo: newPlate,
          isDefault: plateList.length === 0 ? 1 : 0 // 如果是第一个车牌，设为默认
        });
        console.log('apiCreateLicensePlate', res);
        if (res && res.code === 0) {
          // 添加成功，刷新车牌列表
          this.showMessage('添加车牌成功', 'success');
          this.setData({
            showAddPlatePopup: false,
            newPlate: ''
          });

          // 重新获取车牌列表
          await this.fetchPlates();

          // 如果是第一个车牌，自动选择
          if (plateList.length === 0) {
            const newPlateObj = { id: res.data, plateNo: newPlate };
            this.triggerEvent('select', newPlateObj);
            this.close();
          }
        } else {
          this.setData({ loading: false });
          const errorMsg = res && res.msg ? res.msg : '添加车牌失败';
          this.showMessage(errorMsg);
        }
      } catch (error) {
        this.setData({ loading: false });
        console.error('添加车牌错误:', error);
        this.showMessage('添加车牌失败，请重试');
      }
    },

    // 删除车牌
    async deletePlate(e) {
      const plate = e.currentTarget.dataset.plate;

      // 确认删除
      wx.showModal({
        title: '删除车牌',
        content: `确定要删除车牌 ${plate.plateNo} 吗？`,
        confirmColor: '#0052D9',
        success: async (res) => {
          if (res.confirm) {
            try {
              this.setData({ loading: true });

              // 调用删除车牌API
              const deleteRes = await apiDeleteLicensePlate({ id: plate.id });

              if (deleteRes && deleteRes.code === 0) {
                this.showMessage('删除车牌成功', 'success');

                // 如果删除的是当前选中的车牌，通知父组件
                if (this.properties.selectedPlate === plate.plateNo) {
                  this.triggerEvent('delete', plate);
                }

                // 重新获取车牌列表
                await this.fetchPlates();
              } else {
                this.setData({ loading: false });
                const errorMsg = deleteRes && deleteRes.msg ? deleteRes.msg : '删除车牌失败';
                this.showMessage(errorMsg);
              }
            } catch (error) {
              this.setData({ loading: false });
              console.error('删除车牌错误:', error);
              this.showMessage('删除车牌失败，请重试');
            }
          }
        }
      });
    },

    // 关闭弹窗
    close() {
      this.triggerEvent('close');
    },

    // 显示消息
    showMessage(message, type = 'error') {
      wx.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    }
  }
});
