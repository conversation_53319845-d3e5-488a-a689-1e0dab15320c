/* pages/feedback/index.less */
@import '/variable.less';

.feedback-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;
  overflow: hidden;

  // 添加装饰元素 - 圆形装饰
  .decoration-circle-large {
    position: absolute;
    width: 1000rpx;
    height: 1000rpx;
    border-radius: 50%;
    background-color: rgba(0, 82, 217, 0.08); // 品牌蓝色 #0052D9
    top: -400rpx;
    right: -300rpx;
    z-index: 0;
    pointer-events: none;
  }

  .decoration-circle-medium {
    position: absolute;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background-color: rgba(0, 82, 217, 0.05);
    bottom: -200rpx;
    left: -200rpx;
    z-index: 0;
    pointer-events: none;
  }

  .decoration-circle-small {
    position: absolute;
    width: 300rpx;
    height: 300rpx;
    border-radius: 50%;
    background-color: rgba(7, 193, 96, 0.08); // 绿色
    top: 30%;
    left: -100rpx;
    z-index: 0;
    pointer-events: none;
  }
}

// 浮动头部样式 - 简化版本，不包含标题
.feedback-header {
  padding: 16rpx 32rpx;
  z-index: 10;
  min-height: 20rpx; // 保留最小高度以维持页面结构
}

// 主要内容区域
.feedback-content {
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom) + @tab-bar-height);
  position: relative;
  z-index: 1;
  margin-top: 80rpx; // 减小上边距，因为浮动头部不再包含标题
}

// 统一的表单盒子样式
.feedback-form-box {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

// 表单部分样式
.form-section {
  padding: 24rpx 24rpx; // 减小左右内边距，给问题类型更多空间

  .section-title {
    font-size: 30rpx;
    font-weight: 500;
    color: @gy1;
    margin-bottom: 20rpx;
    padding: 0 8rpx; // 添加左右内边距，与问题类型对齐
  }
}

// 表单分隔线
.form-divider {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 0 32rpx;
}

// 问题类型选择样式 - 横向卡片单选框
.problem-types {
  display: flex;
  flex-wrap: wrap; // 改为自动换行，确保所有内容都能显示
  padding: 8rpx 0;
  margin: 0 -8rpx; // 负边距，使两端对齐
  justify-content: flex-start; // 左对齐

  .problem-type-item {
    flex: 0 0 auto; // 不拉伸，不压缩
    height: 64rpx; // 固定高度
    margin: 8rpx; // 四周均匀间距
    padding: 0 20rpx; // 减小水平内边距，使更多项能在一行显示
    box-sizing: border-box;
    border: 1rpx solid #E0E0E0;
    border-radius: 32rpx; // 圆角按钮
    font-size: 26rpx;
    color: @gy2;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.2s ease;
    position: relative;
    white-space: nowrap; // 防止文本换行

    // 激活状态
    &--active {
      color: @brand7-normal;
      border-color: @brand7-normal;
      background-color: rgba(0, 82, 217, 0.05);
      font-weight: 500;
    }

    // 点击效果
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }
}

// 文本域样式
.feedback-textarea {
  width: 100%;
  min-height: 200rpx; // 减小高度
  border: 1rpx solid #E0E0E0; // 减小边框宽度
  border-radius: 8rpx; // 减小圆角
  padding: 16rpx !important; // 减小内边距
  font-size: 28rpx;
  color: @gy1;
  background-color: #fff;

  &--focus {
    border-color: @brand7-normal;
  }
}

// 输入框样式
.feedback-input {
  width: 100%;
  height: 80rpx; // 减小高度
  border: 1rpx solid #E0E0E0; // 减小边框宽度
  border-radius: 8rpx; // 减小圆角
  padding: 0 16rpx !important; // 减小内边距
  font-size: 28rpx;
  color: @gy1;
  background-color: #fff;

  &--focus {
    border-color: @brand7-normal;
  }
}

// 提交按钮样式
.feedback-submit {
  margin-top: 40rpx; // 减小上边距

  .submit-button {
    height: 88rpx; // 减小高度
    font-size: 32rpx; // 减小字体大小
    font-weight: 500;
    border-radius: 44rpx; // 保持圆角
    background-color: @brand7-normal;
    box-shadow: 0 6rpx 12rpx rgba(0, 82, 217, 0.15); // 减小阴影
  }
}

// 自定义 TDesign 组件样式
:host {
  --td-radio-icon-checked-color: @brand7-normal;
  --td-textarea-indicator-text-color: @gy3;
  --td-button-primary-bg-color: @brand7-normal;
  --td-button-primary-active-bg-color: @brand7-light;
}
