import request from './request';

/**
 * 新增车牌
 *
 * 请求参数:
 * - plateNo: 车牌号
 * - isDefault: 是否默认 (1-是, 0-否)
 *
 * 响应数据结构示例:
 * {
 *   "code": 0,
 *   "data": "1924187970378633217", // 车牌ID
 *   "msg": "成功",
 *   "path": null,
 *   "timestamp": "1747597136624",
 *   "errorMsg": null
 * }
 */
export const apiCreateLicensePlate = async (payload) => {
  const { plateNo, isDefault = 1 } = payload;
  const plateRes = await request('/mini/park/licensePlate/create', 'POST', {
    plateNo,
    isDefault
  });
  return plateRes.data;
};

/**
 * 车牌数量
 *
 * 请求参数:
 * - 无
 *
 * 响应数据结构示例:
{
    "code": 0,
    "data": "1",
    "msg": "成功",
    "path": null,
    "timestamp": "1748169627179",
    "errorMsg": null
}
 */
export const apiGetPlateCount = async () => {
  const plateRes = await request('/mini/park/licensePlate/count', 'POST');
  return plateRes.data;
};

/**
 * 删除车牌
 *
 * 请求参数:
 * - id: 车牌ID
 *
 * 响应数据结构示例:
 * {
 *   "code": 0,
 *   "data": null,
 *   "msg": "成功",
 *   "path": null,
 *   "timestamp": "1747597136624",
 *   "errorMsg": null
 * }
 */
export const apiDeleteLicensePlate = async (payload) => {
  const { id } = payload;
  const deleteRes = await request('/mini/park/licensePlate/delete', 'POST', {
    id
  });
  return deleteRes.data;
};

/**
 * 获取车牌列表
 *
 * 响应数据结构示例:
 * {
 *   "code": 0,
 *   "data": {
 *     "curPage": "0",
 *     "pages": "1",
 *     "results": [
 *       {
 *         "id": "1924185283041869826",
 *         "createdBy": "1922104193555427329",
 *         "createdTime": "2025-05-19 03:28:16",
 *         "updatedBy": "1922104193555427329",
 *         "updatedTime": "2025-05-19 03:28:16",
 *         "plateNo": "A34567",
 *         "plateColor": "BLUE",
 *         "isDefault": {
 *           "code": "NO",
 *           "desc": "否"
 *         }
 *       },
 *       {
 *         "id": "1924187865848188930",
 *         "createdBy": "1922104193555427329",
 *         "createdTime": "2025-05-19 03:38:32",
 *         "updatedBy": "1922104193555427329",
 *         "updatedTime": "2025-05-19 03:38:32",
 *         "plateNo": "A34568",
 *         "plateColor": "BLUE",
 *         "isDefault": {
 *           "code": "YES",
 *           "desc": "是"
 *         }
 *       }
 *     ],
 *     "size": "10",
 *     "total": "2",
 *     "extraData": null,
 *     "cursor": null
 *   },
 *   "msg": "成功",
 *   "path": null,
 *   "timestamp": "1747598463931",
 *   "errorMsg": null
 * }
 */
export const apiGetLicensePlateList = async (params = {}) => {
  const { current = 1, size = 10 } = params;
  const listRes = await request('/mini/park/licensePlate/list', 'POST', {
    current,
    size,
    ...params.model
  });
  return listRes.data;
};

/**
 * 设置默认车牌
 *
 * 请求参数:
 * - id: 车牌号表id (从车牌列表item中获取的id字段)
 *
 * 响应数据结构示例:
 * {
 *   "code": 0,
 *   "data": null,
 *   "msg": "成功",
 *   "path": null,
 *   "timestamp": "1747597136624",
 *   "errorMsg": null
 * }
 */
export const apiSetDefaultLicensePlate = async (payload) => {
  const { id } = payload;
  const setDefaultRes = await request('/mini/park/licensePlate/setDefault', 'POST', {
    id
  });
  return setDefaultRes.data;
};
