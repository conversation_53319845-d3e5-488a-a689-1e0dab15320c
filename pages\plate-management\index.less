/* pages/plate-management/index.less */
@import '/variable.less';

.plate-management-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;

  .plate-management-content {
    padding: 24rpx 24rpx 120rpx;
    position: relative;
    z-index: 1;
    margin-top: 30rpx; /* 增加顶部边距，与浮动头部保持距离 */
  }
}

/* 车牌列表 */
.plate-list {
  margin-top: 20rpx;
}

/* 车牌项 */
.plate-item {
  margin-bottom: 30rpx;
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }
}

.plate-card {
  display: flex;
  align-items: center;
  background-color: @bg-color-white;
  border-radius: 30rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 车牌信息区域 */
.plate-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plate-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.plate-number-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.plate-number {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
  letter-spacing: 2rpx;
}

.default-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background-color: @brand7-normal;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  line-height: 1;
  font-weight: 400;
  flex-shrink: 0;
}

.plate-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #959595;
}

.plate-time {
  font-size: 24rpx;
  color: #999999;
}

/* 操作区域 */
.plate-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  min-width: 200rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
}

/* 加载和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: @bg-color-white;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #999999;
}

/* 添加按钮容器 */
.add-button-container {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

/* 添加车牌弹窗 */
.add-plate-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #F0F0F0;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  &__content {
    padding: 30rpx;
  }

  &__footer {
    padding: 30rpx;
    border-top: 1rpx solid #F0F0F0;
    display: flex;
    justify-content: flex-end;
  }
}

.plate-format-hint {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;

  text {
    margin-left: 8rpx;
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .plate-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .plate-actions {
    width: 100%;
    align-items: flex-start;
    margin-top: 16rpx;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}
