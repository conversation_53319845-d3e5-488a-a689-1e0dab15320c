import Message from 'tdesign-miniprogram/message/index';
import { orderApi, lockApi, parkApi } from '~/api/index';
import { formatCoords, openNavigation } from '~/utils/util';

Page({
  data: {
    orderId: '',
    orderInfo: {
      address: '',
      orderDate: '',
      orderTime: '',
      enterDate: '',
      enterTime: '',
      lockDownDate: '',
      lockDownTime: '',
      lockUpTime: '暂无',
      estimatedDuration: '',
      estimatedFee: '',
      exitStatus: '未出场',
      lockStatus: 'down', // 'down' 表示已关锁，'up' 表示已开锁
      plateNo: ''
    },
    // 车位锁规则信息
    lockRuleInfo: {
      address: '',
      location: '',
      spotNumber: '',
      price: '',
      capAmount: '',
      sharedTime: '',
      shareStatus: '',
      latitude: '',
      longitude: ''
    },
    // 停车场信息
    parkingInfo: {
      latitude: '',
      longitude: '',
      name: '',
      address: ''
    },
    showCancelDialog: false,
    showFailDialog: false,
    failMessage: '',
    // 升关锁操作弹窗
    lockOperationPopup: {
      visible: false,
      loading: false,
      text: '',
      showRetry: false,
      overlayProps: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
      }
    },
    // 倒计时相关
    countdownWarningClass: '',
    // 按钮冷却状态
    buttonCooldown: {
      isActive: false,
      remainingTime: 0
    }
  },

  onLoad(options) {
    const { id, lockId } = options;
    if (id) {
      console.log(options);
      this.setData({
        orderId: id,
        lockId: lockId
      });
    }
    this.fetchOrderDetail();
  },



  onUnload() {
    // 组件会自动清理定时器，无需手动处理
    // 清理冷却定时器
    if (this.cooldownTimer) {
      clearInterval(this.cooldownTimer);
      this.cooldownTimer = null;
    }
  },

  // 获取订单详情
  async fetchOrderDetail() {
    if (!this.data.orderId) {
      this.showMessage('订单ID不存在', 'error');
      return;
    }
    try {
      // 显示加载状态
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 调用API获取订单详情
      const orderDetail = await orderApi.apiGetOrderDetail({
        orderId: this.data.orderId
      });

      if (orderDetail && orderDetail.code === 0 && orderDetail.data) {
        // 保存订单数据
        const orderData = orderDetail.data;
        // 先处理订单数据
        this.processOrderData(orderData);
        // 如果有lockId，获取车位锁规则详情和锁状态
        if (orderData.lockId) {
          // 获取车位锁规则详情
          await this.fetchLockRuleDetail(this.data.lockId);
          // 获取当前锁状态
          await this.fetchLockStatus(orderData.lockId);
        }

        // 如果有parkId，获取停车场详情（用于导航）
        if (orderData.parkId) {
          await this.fetchParkingInfo(orderData.parkId);
        }
      } else {
        // 处理错误情况
        const errorMsg = orderDetail?.msg || '获取订单信息失败';
        this.showMessage(errorMsg, 'error');
        console.error('获取订单详情失败:', orderDetail);
      }

      // 关闭加载状态
      wx.hideLoading();
    } catch (error) {
      // 关闭加载状态
      wx.hideLoading();

      // 处理异常
      this.showMessage('获取订单信息失败，请重试', 'error');
      console.error('获取订单详情异常:', error);
    }
  },

  // 获取停车场详情（用于导航）
  async fetchParkingInfo(parkId) {
    if (!parkId) {
      console.error('获取停车场详情失败: 缺少parkId');
      return;
    }
    try {
      // 调用API获取停车场详情
      const parkDetail = await parkApi.apiGetParkDetail({
        id: parkId
      });

      if (parkDetail && parkDetail.code === 0 && parkDetail.data) {
        // 只保存导航需要的经纬度和基本信息
        this.setData({
          parkingInfo: {
            latitude: parkDetail.data.latitude || '',
            longitude: parkDetail.data.longitude || '',
            name: parkDetail.data.parkName || '停车场',
            address: parkDetail.data.parkAddress || ''
          }
        });
        console.log('停车场详情获取成功，经纬度:', parkDetail.data.latitude, parkDetail.data.longitude);
      } else {
        console.error('获取停车场详情失败:', parkDetail);
      }
    } catch (error) {
      console.error('获取停车场详情异常:', error);
    }
  },

  // 获取车位锁规则详情
  async fetchLockRuleDetail(lockId) {
    if (!lockId) {
      console.error('获取车位锁规则详情失败: 缺少lockId');
      return;
    }
    try {
      // 调用API获取车位锁规则详情
      const lockRuleDetail = await lockApi.apiGetLockRuleDetail({
        lockId: lockId
      });

      if (lockRuleDetail && lockRuleDetail.code === 0 && lockRuleDetail.data) {
        // 处理车位锁规则数据
        this.processLockRuleData(lockRuleDetail.data);
        // this.showMessage('车位信息加载成功', 'success');
      } else {
        // 处理错误情况
        console.error('获取车位锁规则详情失败:', lockRuleDetail);
      }
    } catch (error) {
      // 处理异常
      console.error('获取车位锁规则详情异常:', error);
    }
  },

  // 获取车位锁当前状态
  async fetchLockStatus(lockId) {
    if (!lockId) {
      console.error('获取车位锁状态失败: 缺少lockId');
      return;
    }
    try {
      // 调用API获取车位锁状态
      const lockStatusResult = await lockApi.apiCheckLockStatus({
        lockId: lockId
      });

      if (lockStatusResult && lockStatusResult.code === 0 && lockStatusResult.data) {
        // 处理锁状态数据
        const statusCode = lockStatusResult.data.code;
        const lockStatus = statusCode === 'OPEN' ? 'up' : 'down';

        // 更新页面数据
        this.setData({
          'orderInfo.lockStatus': lockStatus
        });

        console.log('获取锁状态成功:', statusCode, '转换为:', lockStatus);
      } else {
        // 处理错误情况
        console.error('获取车位锁状态失败:', lockStatusResult);
      }
    } catch (error) {
      // 处理异常
      console.error('获取车位锁状态异常:', error);
    }
  },

  // 处理车位锁规则数据
  processLockRuleData(lockRuleData) {
    // 格式化共享时间
    let sharedTime = '';
    if (lockRuleData.shareStartTime && lockRuleData.shareEndTime) {
      const isCrossDay = lockRuleData.isCrossDay && lockRuleData.isCrossDay.code === 'YES';
      sharedTime = `每日${lockRuleData.shareStartTime.substring(0, 5)}-${lockRuleData.shareEndTime.substring(0, 5)}${isCrossDay ? '(次日)' : ''}`;
    }

    // 处理共享状态
    let shareStatus = '';
    if (lockRuleData.shareStatus) {
      shareStatus = lockRuleData.shareStatus.code === 'YES' ? '共享中' : '未共享';
    }

    // 更新车位锁规则信息
    this.setData({
      lockRuleInfo: {
        address: lockRuleData.parkAddress || '',
        location: lockRuleData.location || '',
        spotNumber: lockRuleData.code || '',
        price: lockRuleData.price || '',
        capAmount: lockRuleData.capAmount || '',
        sharedTime: sharedTime,
        shareStatus: shareStatus,
        latitude: lockRuleData.latitude || '',
        longitude: lockRuleData.longitude || '',
        lockId: lockRuleData.lockId || ''
      }
    });
  },

  // 处理订单数据
  processOrderData(orderData) {
    // 格式化日期和时间
    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return { date: '', time: '' };
      const parts = dateTimeStr.split(' ');
      return {
        date: parts[0] || '',
        time: parts[1] || ''
      };
    };

    // 处理订单时间
    const orderTime = formatDateTime(orderData.orderTime);
    const arriveDatetime = formatDateTime(orderData.arriveDatetime);
    const lastLockOpenTime = formatDateTime(orderData.lockOpenTime);
    const lastLockCloseTime = formatDateTime(orderData.lastLockCloseTime);
    const leaveDatetime = formatDateTime(orderData.leaveDatetime);

    // 计算预计停车时长（小时）
    const reservationDuration = orderData.reservationDuration ?
      Math.ceil(parseInt(orderData.reservationDuration) / 3600) : 0;

    // 处理锁状态
    const lockStatus = orderData.lockCloseTime ? 'down' : 'up';

    // 更新页面数据
    this.setData({
      orderInfo: {
        // 订单信息
        orderDate: orderTime.date,
        orderTime: orderTime.time || '暂无',
        plateNo: orderData.plateNo || '暂无',
        enterDate: arriveDatetime.date,
        enterTime: arriveDatetime.time || '暂无',
        lockDownDate: lastLockCloseTime.date,
        lockDownTime: lastLockCloseTime.time || '暂无',
        lockUpDate: lastLockOpenTime.date,
        lockUpTime: lastLockOpenTime.time || '暂无',
        estimatedDuration: `${reservationDuration}小时`,
        estimatedFee: orderData.reservationAmount || '0.00',
        exitDate: leaveDatetime.date,
        exitTime: leaveDatetime.time || '未出场',
        lockStatus: lockStatus,

        // 车位信息（从订单接口获取的部分）
        address: orderData.parkAddress || '',

        // 保存锁ID，用于升/关锁操作
        lockId: orderData.lockId || '',

        // 保存预计结束时间，用于倒计时计算
        reservationEndTime: orderData.reservationEndTime || ''
      }
    });

    // 倒计时组件会自动处理 reservationEndTime
  },

  // 倒计时组件事件处理
  onCountdownChange(e) {
    const { totalSeconds } = e.detail;

    // 根据剩余时间设置警告样式
    if (totalSeconds <= 5 * 60) { // 5分钟
      this.setData({ countdownWarningClass: 'danger' });
    } else if (totalSeconds <= 10 * 60) { // 10分钟
      this.setData({ countdownWarningClass: 'warning' });
    } else {
      this.setData({ countdownWarningClass: '' });
    }
  },

  onCountdownTimeout() {
    this.showMessage('共享时间已结束', 'warning');
    console.log('共享时间已结束');
    // 可选：自动刷新订单状态
    // setTimeout(() => {
    //   this.fetchOrderDetail();
    // }, 2000);
  },

  onCountdownWarning(e) {
    const { message } = e.detail;
    this.showMessage(message, 'warning');
  },

  // 返回按钮点击事件
  onBackTap() {
    wx.navigateTo({
      url: '/pages/parking-bill/index',
    })
  },

  // 升/关锁按钮点击事件
  async onLockToggle() {
    // 检查按钮是否在冷却中
    if (this.data.buttonCooldown.isActive) {
      this.showMessage(`请等待 ${this.data.buttonCooldown.remainingTime} 秒后再操作`, 'warning');
      return;
    }

    const lockId = this.data.orderInfo.lockId;

    if (!lockId) {
      this.showMessage('无法获取锁ID，请重试', 'error');
      return;
    }

    // 显示自定义加载弹窗
    this.showLockOperationPopup('检查锁状态中，请稍候...', true);

    try {
      // 先获取当前锁的真实状态
      const lockStatusResult = await lockApi.apiCheckLockStatus({
        lockId: lockId
      });

      if (lockStatusResult && lockStatusResult.code === 0 && lockStatusResult.data) {
        const currentStatus = lockStatusResult.data.code; // 'OPEN' 或 'CLOSE'

        // 根据当前状态决定目标状态（取反）
        const targetStatus = currentStatus === 'OPEN' ? 'CLOSE' : 'OPEN';

        // 更新加载提示
        const operationText = targetStatus === 'OPEN' ? '正在关锁，请稍候...' : '正在开锁，请稍候...';
        this.updateLockOperationPopup(operationText, true);

        // 调用操作锁的API
        await this.operateLock(lockId, targetStatus);

      } else {
        this.hideLockOperationPopup();
        this.showMessage('获取锁状态失败，请重试', 'error');
        console.error('获取锁状态失败:', lockStatusResult);
      }
    } catch (error) {
      this.hideLockOperationPopup();
      this.showMessage('操作失败，请重试', 'error');
      console.error('开关锁操作异常:', error);
    }
  },

  // 操作车锁（统一的升关锁方法）
  async operateLock(lockId, targetStatus) {
    try {
      // 调用新的API操作车锁
      const operateResult = await lockApi.apiOperateLockWithUser({
        lockId: lockId,
        status: targetStatus, // 'OPEN' 或 'CLOSE'
        transmissionType: 0
      });

      if (operateResult && operateResult.code === 0) {
        // 操作API调用成功，等待5秒
        this.updateLockOperationPopup(`控制指令已发送，请等待...`, true);

        // 等待5秒
        await this.delay(5000);

        // 5秒后直接隐藏弹窗，不再验证锁状态
        this.hideLockOperationPopup();

        // 启动按钮冷却
        this.startButtonCooldown();

        // 刷新订单详情
        setTimeout(() => {
          this.fetchOrderDetail();
        }, 1000);

      } else {
        // 操作失败
        const operationName = targetStatus === 'OPEN' ? '关锁' : '开锁';
        const errorMsg = operateResult?.msg || `${operationName}失败`;
        this.hideLockOperationPopup();
        this.showMessage(errorMsg, 'error');
        console.error(`${operationName}失败:`, operateResult);
      }
    } catch (error) {
      // 处理异常
      const operationName = targetStatus === 'OPEN' ? '关锁' : '开锁';
      this.hideLockOperationPopup();
      this.showMessage(`${operationName}失败，请重试`, 'error');
      console.error(`${operationName}异常:`, error);
    }
  },

  // 取消订单按钮点击事件
  onCancelOrder() {
    // 先检查是否可以取消
    // if (this.data.orderInfo.enterTime !== '') {
    //   // 车辆已进场，直接显示失败提示
    //   this.setData({
    //     showFailDialog: true,
    //     failMessage: '车辆已进场，无法取消订单。请出场后提交退款申请。'
    //   });
    //   return;
    // }

    // 显示确认弹窗
    this.setData({ showCancelDialog: true });
  },

  // 确认取消订单
  async onConfirmCancel() {
    // 再次检查是否可以取消（双重保险）
    // if (this.data.orderInfo.enterTime !== '') {
    //   this.setData({
    //     showCancelDialog: false,
    //     showFailDialog: true,
    //     failMessage: '车辆已进场，无法取消订单。请出场后提交退款申请。'
    //   });
    //   return;
    // }

    // 显示加载状态
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    try {
      // 调用API取消订单
      const cancelResult = await orderApi.apiCancelOrder({
        id: this.data.orderId
      });

      // 关闭加载状态
      wx.hideLoading();

      if (cancelResult && cancelResult.code === 0) {
        // 取消成功
        this.setData({ showCancelDialog: false });
        this.showMessage('订单取消成功', 'success');

        // 记录取消操作
        this.logCancelAction();

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack({ delta: 2 });
        }, 1500);
      } else {
        // 取消失败
        this.setData({ showCancelDialog: false });
        const errorMsg = cancelResult?.msg || '取消订单失败';
        this.showMessage(errorMsg, 'error');
        console.error('取消订单失败:', cancelResult);
      }
    } catch (error) {
      // 关闭加载状态
      wx.hideLoading();

      // 处理异常
      this.setData({ showCancelDialog: false });
      this.showMessage('取消订单失败，请重试', 'error');
      console.error('取消订单异常:', error);
    }
  },

  // 记录取消操作
  logCancelAction() {
    // 实际项目中可以记录用户取消订单的行为
    console.log('用户取消订单:', this.data.orderId, '时间:', new Date().toISOString());
  },

  // 关闭取消订单弹窗
  onCancelDialog() {
    this.setData({ showCancelDialog: false });
  },

  // 关闭操作失败弹窗
  onCloseFailDialog() {
    this.setData({ showFailDialog: false });
  },



  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  },

  // 显示升关锁操作弹窗
  showLockOperationPopup(text, loading = false) {
    this.setData({
      lockOperationPopup: {
        visible: true,
        loading: loading,
        text: text,
        showRetry: false,
        overlayProps: {
          backgroundColor: 'rgba(0, 0, 0, 0.6)'
        }
      }
    });
  },

  // 更新升关锁操作弹窗
  updateLockOperationPopup(text, loading = false, showRetry = false) {
    this.setData({
      'lockOperationPopup.text': text,
      'lockOperationPopup.loading': loading,
      'lockOperationPopup.showRetry': showRetry
    });
  },

  // 隐藏升关锁操作弹窗
  hideLockOperationPopup() {
    this.setData({
      'lockOperationPopup.visible': false
    });
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 验证锁状态
  async verifyLockStatus(lockId, expectedStatus) {
    try {
      const lockStatusResult = await lockApi.apiCheckLockStatus({
        lockId: lockId
      });

      if (lockStatusResult && lockStatusResult.code === 0 && lockStatusResult.data) {
        const currentStatus = lockStatusResult.data.code;

        if (currentStatus === expectedStatus) {
          // 状态验证成功
          const lockStatus = expectedStatus === 'OPEN' ? 'up' : 'down';
          // 更新锁状态
          this.setData({
            'orderInfo.lockStatus': lockStatus
          });

          // // 如果是开锁，更新开锁时间
          // if (expectedStatus === 'CLOSE') {
          //   const now = new Date();
          //   const formattedDate = now.toISOString().split('T')[0];
          //   const formattedTime = now.toTimeString().substring(0, 8);
          //   this.setData({
          //     'orderInfo.lockUpTime': `${formattedDate} ${formattedTime}`
          //   });
          // } else {
          //   // 如果是关锁，清除开锁状态
          //   this.setData({
          //     'orderInfo.lockUpStatus': '未开锁'
          //   });
          // }

          this.hideLockOperationPopup();
          // this.showMessage(`${operationName}成功`, 'success');

          // 刷新订单详情
          setTimeout(() => {
            this.fetchOrderDetail();
          }, 1000);

        } else {
          // 状态未变化，显示重试选项
          const operationName = expectedStatus === 'OPEN' ? '关锁' : '开锁';
          this.updateLockOperationPopup(`${operationName}未成功，请重试`, false, true);

          // 保存当前操作信息，用于重试
          this.currentLockOperation = {
            lockId: lockId,
            targetStatus: expectedStatus
          };
        }
      } else {
        // 获取状态失败
        this.updateLockOperationPopup('操作未成功，请重试', false, true);
        console.error('验证锁状态失败:', lockStatusResult);
      }
    } catch (error) {
      // 验证异常
      this.updateLockOperationPopup('操作未成功，请重试', false, true);
      console.error('验证锁状态异常:', error);
    }
  },

  // 重试升关锁操作
  async onRetryLockOperation() {
    // 检查按钮是否在冷却中
    if (this.data.buttonCooldown.isActive) {
      this.showMessage(`请等待 ${this.data.buttonCooldown.remainingTime} 秒后再操作`, 'warning');
      return;
    }

    if (!this.currentLockOperation) {
      this.hideLockOperationPopup();
      this.showMessage('重试失败，请重新操作', 'error');
      return;
    }

    const { lockId, targetStatus } = this.currentLockOperation;
    const operationName = targetStatus === 'OPEN' ? '关锁' : '开锁';

    // 重新显示加载状态
    this.updateLockOperationPopup(`正在重试${operationName}...`, true, false);

    // 重新执行操作
    await this.operateLock(lockId, targetStatus);
  },

  // 取消升关锁操作
  onCancelLockOperation() {
    // 隐藏弹窗
    this.hideLockOperationPopup();

    // 清除当前操作信息
    this.currentLockOperation = null;

    // 显示取消提示
    // this.showMessage('已取消操作', 'info');

    // 可选：刷新页面状态以确保数据同步
    setTimeout(() => {
      this.fetchOrderDetail();
    }, 500);
  },

  // 打开导航
  openNavigation() {
    const { parkingInfo } = this.data;

    // 检查停车场信息是否完整
    if (!parkingInfo.latitude || !parkingInfo.longitude) {
      this.showMessage('无法获取位置信息，请重试', 'error');
      return;
    }

    // 使用工具函数打开导航
    openNavigation({
      latitude: parkingInfo.latitude,
      longitude: parkingInfo.longitude,
      name: parkingInfo.name || '停车场',
      address: parkingInfo.address || ''
    }).catch(err => {
      console.error('导航失败:', err);
      // 错误处理已在 openNavigation 函数中完成
    });
  },

  // 启动按钮冷却
  startButtonCooldown() {
    this.setData({
      'buttonCooldown.isActive': true,
      'buttonCooldown.remainingTime': 30
    });

    // 启动倒计时
    this.cooldownTimer = setInterval(() => {
      const remainingTime = this.data.buttonCooldown.remainingTime - 1;

      if (remainingTime <= 0) {
        // 冷却结束
        this.setData({
          'buttonCooldown.isActive': false,
          'buttonCooldown.remainingTime': 0
        });
        clearInterval(this.cooldownTimer);
        this.cooldownTimer = null;
      } else {
        // 更新倒计时
        this.setData({
          'buttonCooldown.remainingTime': remainingTime
        });
      }
    }, 1000);
  }
});
