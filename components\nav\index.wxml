<!-- <view class="home-navbar {{background ? 'home-navbar--with-bg' : ''}} {{fixed ? 'fixed' : ''}}" style="{{customStyle}}">
  <t-navbar title="{{ navType === 'search' ? '' : titleText }}" background="transparent" fixed="{{fixed}}">
    <view slot="left">
      <view class="home-navbar__left">
        <t-icon class="home-navbar__icon" bind:tap="openDrawer" name="map-information" size="40rpx" />
        <view style="margin-right: 10rpx;width: 15%;">杭州</view>
        <t-search
          shape="round"
          placeholder="请搜索你想要的内容"
          bindtap="searchTurn"
          wx:if="{{navType === 'search'}}"
        />
      </view>
    </view>
  </t-navbar>
  <t-drawer
    style="padding-top: {{statusHeight}}px;"
    visible="{{visible}}"
    items="{{sidebar}}"
    placement="left"
    title="页面目录"
    bind:item-click="itemClick"
  />
</view> -->
<t-navbar   t-class-placeholder="t-navbar-placeholder" t-class-content="t-navbar-content"
  >
  <view slot="left">
    <t-image
      t-class="custom-image"
      src="/assets/head.png"
      aria-label="导航栏图片"
    />
  </view>
  <view slot="left" class="search-box" >
    <t-search disabled="true" shape="round" placeholder="找个空车位" bindtap="searchTurn" />
  </view>

</t-navbar>