import request from './request';

/* apiCreateOrder 响应数据结构示例
  {
    "code": 0,
    "data": "1923565208410341378",
    "msg": "成功",
    "path": null,
    "timestamp": "1747448658635",
    "errorMsg": null
  }
*/
export const apiCreateOrder = async (payload) => {
  const { source = 'MINI_APP', amount = 0, lockId, plateNo, reservationDuration } = payload;
  const orderRes = await request('/mini/park/order/create', 'POST', {
    source,
    amount,
    lockId,
    plateNo,
    reservationDuration
  });
  return orderRes.data;
};

/* apiCancelOrder 响应数据结构示例
  {
    "code": 0,
    "data": "1922475333648973826",
    "msg": "成功",
    "path": null,
    "timestamp": "1747188930128",
    "errorMsg": null
  }
*/
export const apiCancelOrder = async (payload) => {
  const { id } = payload;
  const cancelRes = await request('/mini/park/order/cancel', 'POST', {
    id
  });
  return cancelRes.data;
};

/* apiGetUsingOrders 响应数据结构示例
  {
    "code": 0,
    "data": [
        {
            "id": "1",                                // 订单id
            "lockId": "3001"                          // 车锁id
        },
        {
            "id": "2",                                // 订单id
            "lockId": "3001"                          // 车锁id
        },
        {
            "id": "3",                                // 订单id
            "lockId": "3001"                          // 车锁id
        }
    ],
    "msg": "成功",
    "path": null,
    "timestamp": "1747566611509",
    "errorMsg": null
  }

  注意: 如果返回list长度为0，则表示没有正在使用中的订单；如果长度不为0，则表示有正在使用中的订单
*/
export const apiGetUsingOrders = async (ckAuth) => {
  const ordersRes = await request('/mini/park/order/using', 'POST', {}, ckAuth);
  return ordersRes.data;
};

/* apiGetOrderDetail 响应数据结构示例
  {
    "code": 0,
    "data": {
        "id": "1",                                    // 订单id
        "source": {                                   // 下单方式
            "code": "SCAN",
            "desc": "扫码下单"
        },
        "parkId": "1001",                             // 车场id
        "parkType": {                                 // 停车场类型
            "code": "ENCLOSED_GARAGE",
            "desc": "封闭式车场"
        },
        "lotId": "2001",                              // 车位id
        "lockId": "3001",                             // 车锁id
        "lockNo": "LOCK-20250518-001",                // 车锁编号
        "orderNo": "ORD-20250518-001",                // 订单编号
        "orderType": {                                // 订单类型
            "code": "ODRINARY",
            "desc": "普通计费"
        },
        // 更多订单详细信息...
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747575738099",
    "errorMsg": null
  }
*/
export const apiGetOrderDetail = async (payload) => {
  const { orderId } = payload;
  const orderDetailRes = await request(`/mini/park/order/orderDetail/${orderId}`, 'POST');
  return orderDetailRes.data;
};

/* apiGetOrderList 响应数据结构示例
  {
    "code": 0,
    "data": {
        "curPage": "0",                               // 当前页码
        "pages": "1",                                 // 总页数
        "results": [                                  // 订单列表
            {
                "id": "1",                            // 订单id
                "parkId": "1922843719026405378",      // 停车场id
                "lotId": "1922845474623647745",       // 车位id
                "lockId": "3001",                     // 车锁id
                "parkAddress": "浙江省杭州市上城区停车场", // 停车场地址
                "parkName": "杭州市上城区停车场",       // 停车场名称
                "location": "地下一层",                // 车位区域
                "code": "B101",                       // 区域编码
                "plateNo": "粤A12345",                // 车牌号
                "duration": "7200",                   // 停车时长
                "orderTime": "2025-05-18 08:15:00",   // 下单时间
                "status": {                           // 订单状态
                    "code": "PROCESSING",
                    "desc": "进行中"
                },
                "paySuccessDatetime": "2025-05-18 10:45:00", // 付款时间
                "payableAmount": "25.00",             // 应缴金额
                "parkAmount": "30.00"                 // 停车费用:正常计算的费用
            },
            // 更多订单...
        ],
        "size": "10",                                 // 每页记录数
        "total": "5",                                 // 总记录数
        "extraData": null,                            // 额外数据
        "cursor": null                                // 游标
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747590482480",
    "errorMsg": null
  }
*/
export const apiGetOrderList = async (payload = {}) => {
  const { current = 1, size = 10, model = {} } = payload;
  const orderListRes = await request('/mini/park/order/customerOrder', 'POST', {
    current,
    size,
    model,
    sort: "id"
  });
  return orderListRes.data;
};

/* apiGetCustomerPhone 响应数据结构示例
  {
    "code": 0,
    "data": {
      "phone": "13800138000",
      "customerName": "张三"
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747598463931",
    "errorMsg": null
  }
*/
export const apiGetCustomerPhone = async (payload) => {
  const { lockId } = payload;
  const phoneRes = await request(`/mini/customer/getPhone/${lockId}`, 'POST');
  return phoneRes.data;
};

/* apiCheckLockHasOrder 响应数据结构示例
  {
    "code": 0,
    "data": false,                                     // false：无订单，true：有订单
    "msg": "成功",
    "path": null,
    "timestamp": "1748332142272",
    "errorMsg": null
  }
*/
export const apiCheckLockHasOrder = async (ckAuth) => {
  const lockOrderRes = await request('/device/lock/ifUsingLock', 'POST', {}, ckAuth);
  return lockOrderRes.data;
};
