import Message from 'tdesign-miniprogram/message/index';
import { orderApi, walletApi } from '~/api/index';

Page({
  data: {
    orderId: '',
    loading: true,
    showDetails: false,
    showRefundDialog: false,
    billInfo: {
      // 默认数据，将在API请求成功后被替换
      id: '',
      orderNo: '',
      parkId: '',
      parkName: '',
      parkAddress: '',
      location: '',
      code: '',
      plateNo: '',
      price: '',
      duration: '',
      orderTime: '',
      arriveDatetime: '',
      lockOpenTime: '',
      lastLockCloseTime: '',
      leaveDatetime: '',
      paySuccessDatetime: '',
      remark: '',
      parkingStatus: {
        code: 'NORMAL',
        desc: '未超时'
      },
      status: {
        code: 'COMPLETED',
        desc: '已完成'
      },
      amount: '0.00',
      parkAmount: '0.00',
      timeoutAmount: '0.00',
      preferentialAmount: '0.00',
      payableAmount: '0.00',
      realPayAmount: '0.00',
      unpaidAmount: '0.00',
      isRefunded: false
    }
  },

  onLoad(options) {
    const { orderId } = options;
    if (orderId) {
      this.setData({ orderId: orderId });
      this.fetchBillDetail(orderId);
    } else {
      this.showMessage('订单ID不存在', 'error');
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取账单详情
  async fetchBillDetail(orderId) {
    try {
      // 显示加载状态
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 调用API获取订单详情
      const orderDetail = await orderApi.apiGetOrderDetail({
        orderId: orderId
      });

      // 关闭加载状态
      wx.hideLoading();

      if (orderDetail && orderDetail.code === 0 && orderDetail.data) {
        // 处理订单数据
        this.processBillData(orderDetail.data);
        this.setData({ loading: false });
      } else {
        // 处理错误情况
        const errorMsg = orderDetail?.msg || '获取账单信息失败';
        this.showMessage(errorMsg, 'error');
        console.error('获取账单详情失败:', orderDetail);
        this.setData({ loading: false });
      }
    } catch (error) {
      // 关闭加载状态
      wx.hideLoading();

      // 处理异常
      this.showMessage('获取账单信息失败，请重试', 'error');
      console.error('获取账单详情异常:', error);
      this.setData({ loading: false });
    }
  },

  // 处理账单数据
  processBillData(data) {
    // iOS 兼容的日期解析函数
    const parseDate = (timeStr) => {
      if (!timeStr) return null;

      // 处理不同的日期格式，确保 iOS 兼容性
      let dateStr = timeStr.toString();

      // 如果是时间戳（纯数字），直接使用
      if (/^\d+$/.test(dateStr)) {
        return new Date(parseInt(dateStr));
      }

      // 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 iOS 兼容的 "yyyy/MM/dd HH:mm:ss" 格式
      if (dateStr.includes('-') && dateStr.includes(' ')) {
        dateStr = dateStr.replace(/-/g, '/');
      }
      // 将 "yyyy-MM-dd" 格式转换为 "yyyy/MM/dd" 格式
      else if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        dateStr = dateStr.replace(/-/g, '/');
      }

      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    };

    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return '';

      const date = parseDate(timeStr);
      if (!date) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };

    // 计算停车时长
    const calculateDuration = (duration) => {
      if (!duration) return '未知';

      // 将字符串转换为数字（秒）
      const totalSeconds = parseInt(duration, 10);
      if (isNaN(totalSeconds) || totalSeconds < 0) return '0小时0分';

      // 计算小时和分钟
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);

      return `${hours}小时${minutes}分`;
    };

    // 构建账单信息对象
    const billInfo = {
      id: data.id || '',
      orderNo: data.orderNo || '暂无',
      parkId: data.parkId || '',
      parkName: data.parkName || '暂无',
      parkAddress: data.parkAddress || '暂无',
      location: data.location || '暂无',
      code: data.code || '',
      plateNo: data.plateNo || '暂无',
      price: data.price || '0',
      duration: calculateDuration(data.duration) || '暂无',
      orderTime: formatTime(data.orderTime) || '',
      arriveDatetime: formatTime(data.arriveDatetime) || '',
      lockOpenTime: formatTime(data.lockOpenTime) || '',
      lastLockCloseTime: formatTime(data.lastLockCloseTime) || '',
      leaveDatetime: formatTime(data.leaveDatetime) || '',
      paySuccessDatetime: formatTime(data.paySuccessDatetime) || '',
      remark: data.remark || '未知',
      parkingStatus: data.parkingStatus || {
        code: 'NORMAL',
        desc: '未超时'
      },
      status: data.status || {
        code: 'COMPLETED',
        desc: '已完成'
      },
      amount: data.amount || '0.00',
      parkAmount: data.parkAmount || '0.00',
      timeoutAmount: data.timeoutAmount || '0.00',
      preferentialAmount: data.preferentialAmount || '0.00',
      payableAmount: data.payableAmount || '0.00',
      realPayAmount: data.realPayAmount || '0.00',
      unpaidAmount: data.unpaidAmount || '0.00',
      isRefunded: !!data.refundTime
    };

    this.setData({ billInfo });
  },

  // 复制订单编号
  copyOrderNo() {
    const { orderNo } = this.data.billInfo;
    if (orderNo) {
      wx.setClipboardData({
        data: orderNo,
        success: () => {
          this.showMessage('订单编号已复制', 'success');
        }
      });
    }
  },

  // 切换详情展示
  toggleDetails() {
    this.setData({
      showDetails: !this.data.showDetails
    });
  },

  // 申请退款
  onApplyRefund() {
    this.setData({
      showRefundDialog: true
    });
  },

  // 确认退款
  async onConfirmRefund() {
    this.setData({
      showRefundDialog: false
    });

    try {
      // 显示加载状态
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 这里应该调用退款API
      // 模拟API调用
      setTimeout(() => {
        wx.hideLoading();
        this.showMessage('退款申请已提交，请等待处理', 'success');

        // 更新状态
        const billInfo = { ...this.data.billInfo };
        billInfo.status = {
          code: 'REFUNDING',
          desc: '退款中'
        };
        this.setData({ billInfo });
      }, 1500);
    } catch (error) {
      wx.hideLoading();
      this.showMessage('申请退款失败，请重试', 'error');
      console.error('申请退款异常:', error);
    }
  },

  // 取消退款
  onCancelRefund() {
    this.setData({
      showRefundDialog: false
    });
  },

  // 支付补缴费用
  async onPaySupplementFee() {
    try {
      const { orderId, billInfo } = this.data;
      const unpaidAmount = billInfo.unpaidAmount || '0.00';

      // 检查是否有需要补缴的金额
      if (parseFloat(unpaidAmount) <= 0) {
        this.showMessage('没有需要补缴的金额', 'warning');
        return;
      }

      // 显示加载状态
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 生成唯一ID
      const uniIdRes = await walletApi.apiGenerateUniqueId();
      if (!uniIdRes || !uniIdRes.data) {
        wx.hideLoading();
        this.showMessage('生成支付ID失败，请重试', 'error');
        return;
      }

      // 调用支付API
      const paymentData = await walletApi.apiPayDeposit({
        uniqueId: uniIdRes.data,
        payAmount: unpaidAmount, // 使用待补缴金额
        payType: 'ORDER_SUPPLEMENT', // 订单补缴
        orderId: orderId, // 订单ID
        payMethod: 'WXPAY_LITE' // 微信小程序支付
      });

      // 关闭加载状态
      wx.hideLoading();

      if (!paymentData || !paymentData.data || !paymentData.data.timeStamp) {
        const errorMsg = paymentData && paymentData.msg ? paymentData.msg : '获取支付信息失败';
        this.showMessage(errorMsg, 'error');
        return;
      }

      // 调起微信支付
      wx.requestPayment({
        timeStamp: paymentData.data.timeStamp,
        nonceStr: paymentData.data.nonceStr,
        package: paymentData.data.package,
        signType: paymentData.data.signType || 'RSA',
        paySign: paymentData.data.paySign,
        success: () => {
          // 支付成功
          this.showMessage('补缴支付成功', 'success');

          // 更新状态
          const updatedBillInfo = { ...this.data.billInfo };
          updatedBillInfo.status = {
            code: 'COMPLETED',
            desc: '已完成'
          };
          updatedBillInfo.realPayAmount = updatedBillInfo.payableAmount;
          updatedBillInfo.unpaidAmount = '0.00';
          this.setData({ billInfo: updatedBillInfo });

          // 刷新账单详情
          setTimeout(() => {
            this.fetchBillDetail(orderId);
          }, 1000);
        },
        fail: (err) => {
          // 支付失败
          console.error('补缴支付失败:', err);
          if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
            this.showMessage('支付已取消', 'warning');
          } else {
            this.showMessage('支付失败，请重试', 'error');
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      this.showMessage('支付过程中出现错误，请重试', 'error');
      console.error('支付异常:', error);
    }
  },

  // 返回上一页
  onBackTap() {
    wx.navigateBack();
  },

  // 显示消息提示
  showMessage(message, type = 'success') {
    Message.info({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content: message,
      theme: type,
    });
  }
});
