import { plateApi } from '~/api/index';
import Message from 'tdesign-miniprogram/message';

Page({
  data: {
    plateList: [],
    loading: false,
    showAddDialog: false,
    newPlate: '',
    editingPlate: null,
    showEditDialog: false,
    editPlateValue: ''
  },

  onLoad() {
    // 页面加载时获取车牌列表
    this.fetchPlateList();
  },

  onShow() {
    // 页面显示时刷新车牌列表
    this.fetchPlateList();
  },

  // 获取车牌列表
  async fetchPlateList() {
    try {
      this.setData({ loading: true });

      const res = await plateApi.apiGetLicensePlateList();
      console.log('获取车牌列表API响应:', res);
      
      if (res && res.code === 0 && res.data) {
        const plates = res.data.results || res.data || [];

        if (Array.isArray(plates)) {
          // 处理车牌数据
          const plateNumbers = plates.map(plate => ({
            id: plate.id,
            plateNo: plate.plateNo,
            plateColor: plate.plateColor,
            isDefault: plate.isDefault && plate.isDefault.code === 'YES',
            createdTime: plate.createdTime
          }));

          // 按创建时间排序，默认车牌排在前面
          plateNumbers.sort((a, b) => {
            if (a.isDefault && !b.isDefault) return -1;
            if (!a.isDefault && b.isDefault) return 1;
            return new Date(b.createdTime) - new Date(a.createdTime);
          });

          this.setData({
            plateList: plateNumbers,
            loading: false
          });
        } else {
          this.setData({ plateList: [], loading: false });
          this.showMessage('车牌列表数据格式错误');
        }
      } else {
        this.setData({ plateList: [], loading: false });
        this.showMessage('获取车牌列表失败');
      }
    } catch (error) {
      console.error('获取车牌列表错误:', error);
      this.setData({ plateList: [], loading: false });
      this.showMessage('获取车牌列表失败');
    }
  },

  // 显示添加车牌弹窗
  showAddPlateDialog() {
    this.setData({ 
      showAddDialog: true,
      newPlate: ''
    });
  },

  // 关闭添加车牌弹窗
  closeAddDialog() {
    this.setData({ 
      showAddDialog: false,
      newPlate: ''
    });
  },

  // 输入新车牌号
  onNewPlateInput(e) {
    this.setData({
      newPlate: e.detail.value.toUpperCase()
    });
  },

  // 添加新车牌
  async addNewPlate() {
    const { newPlate, plateList } = this.data;

    if (!newPlate.trim()) {
      this.showMessage('请输入车牌号');
      return;
    }

    // 车牌号验证
    const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5,6}$/;
    if (!plateRegex.test(newPlate)) {
      this.showMessage('请输入正确的车牌号格式');
      return;
    }

    // 检查是否已存在
    const plateExists = plateList.some(plate => plate.plateNo === newPlate);
    if (plateExists) {
      this.showMessage('该车牌已存在');
      return;
    }

    try {
      this.setData({ loading: true });

      const res = await plateApi.apiCreateLicensePlate({
        plateNo: newPlate,
        isDefault: plateList.length === 0 ? 1 : 0
      });

      if (res && res.code === 0) {
        this.showMessage('添加车牌成功', 'success');
        this.closeAddDialog();
        await this.fetchPlateList();
      } else {
        this.setData({ loading: false });
        const errorMsg = res && res.msg ? res.msg : '添加车牌失败';
        this.showMessage(errorMsg);
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('添加车牌错误:', error);
      this.showMessage('添加车牌失败，请重试');
    }
  },

  // 删除车牌
  async deletePlate(e) {
    const plate = e.currentTarget.dataset.plate;

    wx.showModal({
      title: '删除车牌',
      content: `确定要删除车牌 ${plate.plateNo} 吗？`,
      confirmColor: '#0052D9',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ loading: true });

            const deleteRes = await plateApi.apiDeleteLicensePlate({ id: plate.id });

            if (deleteRes && deleteRes.code === 0) {
              this.showMessage('删除车牌成功', 'success');
              await this.fetchPlateList();
            } else {
              this.setData({ loading: false });
              const errorMsg = deleteRes && deleteRes.msg ? deleteRes.msg : '删除车牌失败';
              this.showMessage(errorMsg);
            }
          } catch (error) {
            this.setData({ loading: false });
            console.error('删除车牌错误:', error);
            this.showMessage('删除车牌失败，请重试');
          }
        }
      }
    });
  },

  // 显示消息
  showMessage(message, type = 'error') {
    Message.info({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      single: true,
      content: message
    });
  }
});
