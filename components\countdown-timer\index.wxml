<view class="countdown-timer {{customClass}}" style="{{customStyle}}">
  <!-- 超时状态 -->
  <view wx:if="{{isTimeout}}" class="countdown-timeout">
    <text class="timeout-text">{{timeoutText}}</text>
  </view>
  
  <!-- 正常倒计时状态 -->
  <view wx:elif="{{isActive}}" class="countdown-active">
    <!-- HMS格式显示 -->
    <view wx:if="{{format === 'hms'}}" class="countdown-hms">
      <text class="time-unit">{{countdown.hours}}</text>
      <text class="time-separator">:</text>
      <text class="time-unit">{{countdown.minutes}}</text>
      <text class="time-separator">:</text>
      <text class="time-unit">{{countdown.seconds}}</text>
    </view>
    
    <!-- 文本格式显示 -->
    <view wx:elif="{{format === 'text'}}" class="countdown-text">
      <text class="time-text">{{countdown.hours}}小时{{countdown.minutes}}分{{countdown.seconds}}秒</text>
    </view>
    
    <!-- 自定义内容插槽 -->
    <slot name="custom" wx:elif="{{format === 'custom'}}" />
  </view>
  
  <!-- 未激活状态 -->
  <view wx:else class="countdown-inactive">
    <text class="inactive-text">--:--:--</text>
  </view>
</view>
