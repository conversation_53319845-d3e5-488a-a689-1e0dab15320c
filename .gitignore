# Ignore doc folder
doc/

# WeChat Mini Program specific
miniprogram_npm/
node_modules/
.DS_Store
__MACOSX/
.idea/
.vscode/
*.swp
*.swo

# npm
npm-debug.log
package-lock.json
yarn.lock
yarn-error.log

# Build files
dist/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# System Files
.DS_Store
Thumbs.db
