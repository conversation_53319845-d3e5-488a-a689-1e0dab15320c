<!-- 车牌管理页面 -->
<view class="car-management-container">
  <!-- 渐变导航栏 -->
  <gradient-navbar title="车牌管理" />

  <!-- 浮动头部 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{100}}">
    <view slot="main" class="header-main">
      <view class="header-title">车牌管理</view>
    </view>
    <view slot="secondary" class="header-secondary">
      <view class="header-subtitle">管理您的车牌信息</view>
    </view>
  </floating-header>

  <!-- 页面内容 -->
  <view class="car-management-content">
    <!-- 加载状态 -->
    <view wx:if="{{loading && plateList.length === 0}}" class="loading-container">
      <t-loading theme="circular" size="40rpx" />
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 车牌列表 -->
    <view wx:elif="{{plateList.length > 0}}" class="plate-list">
      <view 
        class="plate-item"
        wx:for="{{plateList}}"
        wx:key="id"
      >
        <t-swipe-cell>
          <!-- 车牌卡片内容 -->
          <view slot="content" class="plate-card">
            <!-- 左侧车牌图标 -->
            <view class="plate-icon-container">
              <t-icon name="location-parking-place" size="60rpx" color="#0052D9" />
            </view>

            <!-- 中间车牌信息 -->
            <view class="plate-info">
              <view class="plate-header">
                <view class="plate-number">{{item.plateNo}}</view>
                <t-tag wx:if="{{item.isDefault}}" theme="primary" size="small">默认</t-tag>
              </view>
              <view class="plate-meta">
                <view class="plate-color">{{item.plateColor === 'BLUE' ? '蓝牌' : '其他'}}</view>
                <view class="plate-time">{{item.createdTime}}</view>
              </view>
            </view>

            <!-- 右侧操作图标 -->
            <view class="plate-actions">
              <t-icon name="chevron-right" size="32rpx" color="#CCCCCC" />
            </view>
          </view>

          <!-- 右滑删除操作 -->
          <view slot="right" class="swipe-actions">
            <view 
              class="delete-action"
              bindtap="deletePlate"
              data-plate="{{item}}"
            >
              <t-icon name="delete" size="36rpx" color="#FFFFFF" />
              <text>删除</text>
            </view>
          </view>
        </t-swipe-cell>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <t-icon name="location-parking-place" size="120rpx" color="#CCCCCC" />
      <view class="empty-title">暂无车牌</view>
      <view class="empty-subtitle">添加您的车牌信息，方便快速选择</view>
      <t-button 
        theme="primary" 
        size="medium"
        bindtap="showAddPlateDialog"
        style="margin-top: 32rpx;"
      >
        添加车牌
      </t-button>
    </view>

    <!-- 添加车牌按钮 -->
    <view wx:if="{{plateList.length > 0}}" class="add-plate-section">
      <t-button 
        theme="primary" 
        block 
        size="large"
        icon="add"
        bindtap="showAddPlateDialog"
      >
        添加新车牌
      </t-button>
    </view>
  </view>
</view>

<!-- 添加车牌弹窗 -->
<t-popup 
  visible="{{showAddDialog}}" 
  placement="center"
  bind:visible-change="closeAddDialog"
>
  <view class="add-plate-dialog">
    <view class="dialog-header">
      <view class="dialog-title">添加车牌</view>
      <t-icon 
        name="close" 
        size="44rpx" 
        color="#999999" 
        bindtap="closeAddDialog" 
      />
    </view>
    
    <view class="dialog-content">
      <t-input
        label="车牌号"
        placeholder="请输入车牌号，如：浙A12345"
        value="{{newPlate}}"
        bind:change="onNewPlateInput"
        maxlength="8"
        style="margin-bottom: 24rpx;"
      />
      
      <view class="plate-format-hint">
        <t-icon name="info-circle" size="32rpx" color="#999999" />
        <text>支持全国各省市车牌格式</text>
      </view>
    </view>
    
    <view class="dialog-footer">
      <t-button 
        theme="light" 
        size="large"
        bindtap="closeAddDialog"
        style="margin-right: 16rpx; flex: 1;"
      >
        取消
      </t-button>
      <t-button 
        theme="primary" 
        size="large"
        bindtap="addNewPlate" 
        loading="{{loading}}"
        style="flex: 1;"
      >
        确认添加
      </t-button>
    </view>
  </view>
</t-popup>

<!-- 消息提示 -->
<t-message id="t-message" />
