@import '/variable.less';

/* 主容器样式 */
.order-detail-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  padding-bottom: 0; /* 移除底部内边距，由内容区域的padding来控制 */
  box-sizing: border-box;
  position: relative;
}



/* 装饰性元素 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 1;

  &.top-right {
    top: -100rpx;
    right: -100rpx;
    width: 300rpx;
    height: 300rpx;
    background: rgba(255, 255, 255, 0.1);
  }

  &.bottom-left {
    bottom: -80rpx;
    left: -80rpx;
    width: 200rpx;
    height: 200rpx;
    background: rgba(255, 255, 255, 0.08);
  }

  &.bottom-right {
    bottom: 60rpx;
    right: 40rpx;
    width: 120rpx;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.05);
  }
}

/* 倒计时区域 */
.countdown-section {
  text-align: center;
  // margin: 20rpx 0 40rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 16rpx;
  font-weight: 500;
  text-align: center;
}

/* 倒计时组件容器样式 */
.countdown-section countdown-timer {
  /* 组件自带样式，这里可以添加容器级别的样式调整 */
}

.countdown-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 10rpx;
  text-align: center;
}

/* 共享时段区域 */
.shared-time-section {
  margin: 10rpx auto 40rpx;
  text-align: center;
  position: relative;
  z-index: 2;
}

.shared-time-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);

  t-icon {
    margin-right: 8rpx;
  }

  text {
    font-weight: 400;
  }
}

/* 温馨提示卡片 */
.notice-card {
  margin-bottom: 60rpx; /* 确保底部有足够的空间 */
}

.notice-content {
  padding: 0;
}

.notice-item {
  font-size: 26rpx;
  color: @gy2;
  line-height: 1.6;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 内容区域 */
.order-detail-content {
  padding: 50rpx 24rpx 220rpx; /* 大幅增加上下padding，确保内容与浮动层有充分间距 */
  position: relative;
  z-index: 1;
  background-color: #F7F8FA;
  margin-top: 40rpx; /* 增加顶部margin，与悬浮倒计时区域保持更大距离 */
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 36rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .navigation-btn {
    display: flex;
    align-items: center;
    background-color: rgba(0, 82, 217, 0.1);
    padding: 6rpx 16rpx;
    border-radius: 24rpx;
    margin-right: 10rpx;

    text {
      font-size: 24rpx;
      color: @brand7-normal;
      margin-left: 4rpx;
    }

    &:active {
      opacity: 0.8;
    }
  }
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: @gy1;
  position: relative;
  padding-left: 16rpx;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 24rpx;
    background-color: @brand7-normal;
    border-radius: 3rpx;
  }
}

/* 信息项样式 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: @gy2;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: @gy1;
  text-align: right;
  flex: 1;

  .date {
    margin-right: 12rpx;
  }

  .time {
    color: @gy2;
  }

  .status {
    color: #FA9D3B;
  }

  .plate-no {
    font-weight: 500;
    color: @gy1;
    letter-spacing: 1rpx;
  }

  /* 移除了address-container样式 */

  .status-tag {
    display: inline-block;
    font-size: 22rpx;
    color: #fff;
    background-color: @brand7-normal;
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
  }
}

/* 分隔线 */
.divider {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 6rpx 0;
  opacity: 0.8;
}



/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 30rpx 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 95; /* 高于内容，低于导航栏 */
  border-top: 1rpx solid #f0f0f0;
}

/* 主要操作按钮（升/降锁） */
.main-action-button {
  width: 90% !important;
  height: 88rpx !important;
  font-size: 32rpx !important;
  border-radius: 44rpx !important;
  font-weight: 500 !important;
  letter-spacing: 2rpx !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.12) !important;
  margin: 0 0 16rpx 0 !important;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 4rpx rgba(0, 82, 217, 0.12) !important;
  }
}

/* 取消订单链接 */
.cancel-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  background: transparent;
  border: none;
  width: 100%;

  t-icon {
    margin-right: 8rpx;
  }

  text {
    font-size: 24rpx;
    color: #AAAAAA;
  }
}

/* 取消链接悬停效果 */
.cancel-link-hover {
  opacity: 0.8;
}

/* 升降锁操作弹窗样式 */
.lock-operation-popup {
  .lock-operation-content {
    background-color: @bg-color-white;
    border-radius: 24rpx; /* 使用项目标准圆角 */
    padding: 64rpx 48rpx 48rpx;
    text-align: center;
    min-width: 480rpx;
    max-width: 540rpx;
    position: relative;
    /* 使用TDesign标准阴影 */
    box-shadow: 0 6rpx 30rpx 5rpx rgba(0, 0, 0, 0.05),
                0 16rpx 24rpx 2rpx rgba(0, 0, 0, 0.04),
                0 8rpx 10rpx -5rpx rgba(0, 0, 0, 0.08);

    /* 添加装饰性元素 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 6rpx;
      background-color: @brand7-normal;
      border-radius: 3rpx;
      opacity: 0.8;
    }

    .lock-operation-status {
      margin-bottom: 32rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120rpx;

      .status-loading,
      .status-warning {
        position: relative;

      }

      .status-loading {
        &::before {
          background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(0, 82, 217, 0.05));
          animation: pulse 2s ease-in-out infinite;
        }

        .lock-operation-loading {
          .t-icon {
            color: @brand7-normal;
            font-size: 56rpx !important;
            position: relative;
            z-index: 1;
            animation: rotate 1.5s linear infinite;
            filter: drop-shadow(0 2rpx 8rpx rgba(0, 82, 217, 0.2));
          }
        }
      }

      .status-warning {
        &::before {
          background: linear-gradient(135deg, rgba(250, 157, 59, 0.1), rgba(250, 157, 59, 0.05));
        }

        .t-icon {
          position: relative;
          z-index: 1;
          filter: drop-shadow(0 2rpx 8rpx rgba(250, 157, 59, 0.2));
        }
      }
    }

    .lock-operation-text {
      font-size: 32rpx;
      color: @gy1;
      font-weight: 500;
      margin-bottom: 24rpx;
      line-height: 1.5;
      letter-spacing: 0.5rpx;
    }

    .lock-operation-actions {
      margin-top: 32rpx;
      padding-top: 24rpx;
      border-top: 1rpx solid #F0F0F0;
      display: flex;
      justify-content: center;
      gap: 24rpx;

      .cancel-button,
      .retry-button {
        width: 140rpx !important;
        height: 64rpx !important;
        font-size: 28rpx !important;
        border-radius: 32rpx !important;
        font-weight: 500 !important;
        letter-spacing: 1rpx !important;

        &:active {
          transform: translateY(1rpx);
        }
      }

      .cancel-button {
        background-color: #F7F8FA !important;
        color: @gy2 !important;
        border: 1rpx solid #E7E7E7 !important;

        &:active {
          background-color: #EEEFF0 !important;
        }
      }

      .retry-button {
        box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.15) !important;

        &:active {
          box-shadow: 0 2rpx 8rpx rgba(0, 82, 217, 0.15) !important;
        }
      }
    }
  }
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 弹窗进入动画 */
@keyframes popup-scale-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 弹窗内容动画增强 */
.lock-operation-popup {
  .lock-operation-content {
    animation: popup-scale-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}
