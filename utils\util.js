const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};

const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};

// 复制到本地临时路径，方便预览
const getLocalUrl = (path, name) => {
  const fs = wx.getFileSystemManager();
  const tempFileName = `${wx.env.USER_DATA_PATH}/${name}`;
  fs.copyFileSync(path, tempFileName);
  return tempFileName;
};
// 提取URL内全部查询参数
const extractParam = (str) => {
  try {
    // 使用正则表达式提取查询字符串部分（?后面到#、空格或字符串结尾）
    const queryMatch = str.match(/\?([^#\s]*)/);
    if (!queryMatch) return {};

    const queryString = queryMatch[1];
    const params = {};

    // 分割参数并构建结果对象
    queryString.split('&').forEach(param => {
      if (param) {
        const [key, value = ''] = param.split('=');
        if (key) {
          try {
            params[decodeURIComponent(key)] = decodeURIComponent(value);
          } catch (e) {
            params[key] = value;
          }
        }
      }
    });

    return params;
  } catch (e) {
    console.error('URL参数解析失败:', e);
    return {};
  }
}

const splitTimeStr = (a, b, c = false) => {
  const validate = (timeStr) => {
    if (typeof timeStr !== 'string' || timeStr.length < 5) {
      return '00:00'
    }
    return timeStr.substring(0, 5);
  };

  return typeof b !== 'undefined'
    ? `${validate(a)} - ${c ? '次日' : ''}${validate(b)}`
    : validate(a);
}
// 按字段进行归类排序
const sortArrByContent = (arrParam, target) => {

  return arrParam.sort((a, b) => {
    const ai = a[target]
    const bi = b[target]
    return (ai == bi) ? 0 : (ai ? -1 : 1)
  })
}
// 按余位进行归类排序
const sortArrBySlots = (arrParam, target) => {
  return arrParam.sort((a, b) => {
    const ai = a[target]
    const bi = b[target]
    return (ai == bi) ? 0 : (bi==0 ? -1 : 1)
  })
}
// 格式化距离
const formatDistance = (distance) => {
  if (!distance) return '未知距离';
  // 将字符串转为数字
  const dist = parseFloat(distance);
  if (isNaN(dist)) return '未知距离';
  // 如果距离大于1000米，则显示为千米
  if (dist >= 1000) {
    return (dist / 1000).toFixed(1) + 'km';
  }
  // 否则显示为米
  return Math.round(dist) + 'm';
}
//格式化坐标数值
const formatCoords = (param) => {
  if (param == '') return ''
  const strArr = param.split('.')
  return parseFloat(`${strArr[0]}.${strArr[1].substring(0, 3)}`)
}
// 打开地图导航
const openNavigation = (options = {}) => {
  const { latitude, longitude, name = '目的地', address = '' } = options;

  // 参数验证
  if (!latitude || !longitude) {
    wx.showToast({
      title: '位置信息不完整',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(new Error('缺少经纬度信息'));
  }

  // 验证经纬度格式
  const lat = formatCoords(latitude);
  const lng = formatCoords(longitude);

  if (isNaN(lat) || isNaN(lng)) {
    wx.showToast({
      title: '位置信息格式错误',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(new Error('经纬度格式错误'));
  }

  // 验证经纬度范围
  if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
    wx.showToast({
      title: '位置信息超出范围',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(new Error('经纬度超出有效范围'));
  }

  return new Promise((resolve, reject) => {
    wx.openLocation({
      latitude: lat,
      longitude: lng,
      scale: 18, // 地图缩放级别，范围5~18
      success: (res) => {
        console.log('导航成功:', res);
        resolve(res);
      },
      fail: (err) => {
        console.error('导航失败:', err);
        wx.showToast({
          title: '打开导航失败',
          icon: 'none',
          duration: 2000
        });
        reject(err);
      }
    });
  });
};

module.exports = {
  extractParam,
  sortArrByContent,
  sortArrBySlots,
  formatDistance,
  splitTimeStr,
  formatTime,
  getLocalUrl,
  openNavigation,
  formatCoords,
};
