import request from './request';

// 导入所有 API 模块
import * as lockApi from './lock';
import * as userApi from './user';
import * as parkApi from './park';
import * as feedbackApi from './feedback';
import * as walletApi from './wallet';
import * as orderApi from './order';
import * as earningApi from './earning';
import * as plateApi from './plate';

// 导出所有 API 模块
export {
  lockApi,
  userApi,
  parkApi,
  feedbackApi,
  walletApi,
  orderApi,
  earningApi,
  plateApi,
  request
};

// 导出所有 API 方法（兼容旧代码）
export default {
  ...lockApi,
  ...userApi,
  ...parkApi,
  ...feedbackApi,
  ...walletApi,
  ...orderApi,
  ...earningApi,
  ...plateApi,
};
