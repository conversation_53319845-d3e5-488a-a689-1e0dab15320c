<!-- 倒计时组件演示页面 -->
<view class="demo-container">
  <view class="demo-section">
    <view class="demo-title">基础倒计时</view>
    <countdown-timer 
      end-time="{{futureTime}}"
      bind:change="onCountdownChange"
      bind:timeout="onCountdownTimeout"
      bind:warning="onCountdownWarning"
    />
  </view>

  <view class="demo-section">
    <view class="demo-title">文本格式</view>
    <countdown-timer 
      end-time="{{futureTime}}"
      format="text"
    />
  </view>

  <view class="demo-section">
    <view class="demo-title">已超时状态</view>
    <countdown-timer 
      end-time="{{pastTime}}"
      timeout-text="时间已到"
    />
  </view>

  <view class="demo-section">
    <view class="demo-title">警告状态</view>
    <countdown-timer 
      end-time="{{warningTime}}"
      custom-class="warning"
    />
  </view>

  <view class="demo-section">
    <view class="demo-title">危险状态</view>
    <countdown-timer 
      end-time="{{dangerTime}}"
      custom-class="danger"
    />
  </view>

  <view class="demo-controls">
    <button bindtap="updateTimes">更新时间</button>
  </view>
</view>
