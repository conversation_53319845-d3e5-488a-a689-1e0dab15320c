// pages/feedback/index.js
import { Message } from 'tdesign-miniprogram';
import { feedbackApi } from '~/api/index';

Page({
  data: {
    // 问题类型选项
    problemTypes: [
      { value: 'payment', label: '扣款异常', checked: true },
      { value: 'loading', label: '页面加载失败' },
      { value: 'deposit', label: '保证金未返还' },
      { value: 'lock', label: '升降锁异常' },
      { value: 'other', label: '其他' }
    ],
    // 表单数据
    formData: {
      problemType: 'payment', // 默认选中第一项
      description: '',
      contact: ''
    },
    // 字数限制
    maxLength: 200,
    // 页面滚动位置
    scrollTop: 0
  },

  // 页面滚动事件
  onPageScroll(e) {
    this.setData({
      scrollTop: e.scrollTop
    });
  },

  // 问题类型选择变化
  onProblemTypeChange(e) {
    // 支持两种方式：radio-group 的 change 事件和自定义点击事件
    const value = e.detail?.value || e.currentTarget.dataset.value;

    if (value) {
      this.setData({
        'formData.problemType': value
      });
    }
  },

  // 问题描述输入变化
  onDescriptionChange(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  // 联系方式输入变化
  onContactChange(e) {
    this.setData({
      'formData.contact': e.detail.value
    });
  },

  // 提交表单
  submitFeedback() {
    const { formData } = this.data;

    // 表单验证
    if (!formData.description.trim()) {
      this.showMessage('请描述您的问题', 'error');
      return;
    }

    if (!formData.contact.trim()) {
      this.showMessage('请填写您的联系方式', 'error');
      return;
    }

    // 手机号码简单验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contact)) {
      this.showMessage('请输入正确的手机号码', 'error');
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 将问题类型转换为字符串枚举类型（根据最新接口文档）
    const problemTypeMap = {
      'payment': 'DEDUCTION_ISSUE',        // 扣款异常
      'loading': 'PAGE_LOADING_FAILURE',   // 页面加载失败
      'deposit': 'DEPOSIT_NOT_REFUNDED',   // 保证金未退还
      'lock': 'LOCK_OPERATION_ISSUE',      // 升降锁异常
      'other': 'OTHER'                     // 其他
    };

    // 调用API提交反馈
    feedbackApi.apiCreateFeedback({
      type: problemTypeMap[formData.problemType] || 'OTHER', // 默认为其他
      remark: formData.description,
      phone: formData.contact
    })
      .then(res => {
        wx.hideLoading();

        if (res && res.code === 0) {
          // 提交成功
          this.showMessage('反馈提交成功，感谢您的反馈！', 'success');

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          // 提交失败
          this.showMessage(res.msg || '提交失败，请稍后重试', 'error');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('提交反馈失败:', error);
        this.showMessage('提交失败，请稍后重试', 'error');
      });
  },

  // 返回上一页
  onBack() {
    wx.navigateBack();
  },

  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 3000,
      content,
    });
  }
});
