import Message from 'tdesign-miniprogram/message';
import { parkApi } from '~/api/index';
import { formatDistance } from '~/utils/util';

Page({
  data: {
    searchValue: '',
    searchResults: [],
    historyWords: [],
    popularWords: [],
    loading: false,
    dialog: {
      title: '确认删除当前历史记录',
      showCancelButton: true,
      message: '',
    },
    dialogShow: false
  },

  deleteType: 0,
  deleteIndex: '',

  onLoad(options) {
    // 如果有传入的搜索关键词，则自动搜索
    if (options.keyword) {
      this.setData({
        searchValue: options.keyword
      });
      this.searchParkings(options.keyword);
    }

    // 获取历史记录和热门搜索
    this.queryHistory();
    this.queryPopular();
  },

  // 查询历史记录
  async queryHistory() {
    try {
      // 从本地存储获取历史记录
      const historyWords = wx.getStorageSync('searchHistory') || [];
      this.setData({ historyWords });
    } catch (error) {
      console.error('获取历史记录失败:', error);
      // 使用模拟数据
      this.setData({
        historyWords: ['南北商务港', '丰盛九玺', '新青年广场', '金溪园']
      });
    }
  },

  // 查询热门搜索
  async queryPopular() {
    try {
      // 使用模拟数据，实际项目中可以调用真实的API
      this.setData({
        popularWords: ['杭州西湖', '城西银泰', '西溪湿地', '钱江新城', '万象城', '滨江区']
      });
    } catch (error) {
      console.error('获取热门搜索失败:', error);
      // 使用模拟数据
      this.setData({
        popularWords: ['杭州西湖', '城西银泰', '西溪湿地', '钱江新城']
      });
    }
  },

  // 搜索提交
  onSearchSubmit(e) {
    const value = e.detail.value;
    if (!value.trim()) {
      this.showMessage('请输入搜索内容');
      return;
    }

    // 更新搜索值
    this.setData({ searchValue: value });

    // 保存到历史记录
    this.setHistoryWords(value);

    // 执行搜索
    this.searchParkings(value);
  },

  // 搜索取消
  onSearchCancel() {
    wx.navigateBack();
  },

  // 设置历史记录
  setHistoryWords(searchValue) {
    if (!searchValue) return;

    const { historyWords } = this.data;
    const index = historyWords.indexOf(searchValue);

    if (index !== -1) {
      historyWords.splice(index, 1);
    }
    historyWords.unshift(searchValue);

    // 限制历史记录数量，最多保存10条
    if (historyWords.length > 10) {
      historyWords.splice(10);
    }

    // 保存到本地存储
    try {
      wx.setStorageSync('searchHistory', historyWords);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }

    this.setData({
      searchValue,
      historyWords,
    });
  },

  // 点击历史记录
  handleHistoryTap(e) {
    const { historyWords } = this.data;
    const { index } = e.currentTarget.dataset;
    const searchValue = historyWords[index || 0] || '';

    if (!searchValue.trim()) return;

    // 更新搜索值
    this.setData({ searchValue });

    // 保存到历史记录
    this.setHistoryWords(searchValue);

    // 执行搜索
    this.searchParkings(searchValue);
  },

  // 点击热门搜索
  handlePopularTap(e) {
    const { popularWords } = this.data;
    const { index } = e.currentTarget.dataset;
    const searchValue = popularWords[index || 0] || '';

    if (!searchValue.trim()) return;

    // 更新搜索值
    this.setData({ searchValue });

    // 保存到历史记录
    this.setHistoryWords(searchValue);

    // 执行搜索
    this.searchParkings(searchValue);
  },

  // 清空历史记录
  handleClearHistory() {
    const { dialog } = this.data;
    this.deleteType = 1;
    this.setData({
      dialog: {
        ...dialog,
        message: '确认删除所有历史记录',
      },
      dialogShow: true,
    });
  },

  // 确认对话框
  confirm() {
    const { historyWords } = this.data;
    const { deleteType, deleteIndex } = this;

    if (deleteType === 0) {
      historyWords.splice(deleteIndex, 1);
      // 更新本地存储
      try {
        wx.setStorageSync('searchHistory', historyWords);
      } catch (error) {
        console.error('更新历史记录失败:', error);
      }
      this.setData({
        historyWords,
        dialogShow: false,
      });
    } else {
      // 清空所有历史记录
      try {
        wx.removeStorageSync('searchHistory');
      } catch (error) {
        console.error('清空历史记录失败:', error);
      }
      this.setData({ historyWords: [], dialogShow: false });
    }
  },

  // 关闭对话框
  close() {
    this.setData({ dialogShow: false });
  },

  // 搜索停车场
  async searchParkings(keyword) {
    if (!keyword.trim()) return;

    this.setData({ loading: true });

    try {
      // 使用 apiSearchParks 接口进行搜索
      const res = await parkApi.apiSearchParks({
        parkName: keyword,
        current: 1,
        size: 20
      });

      if (res && res.code === 0 && res.data) {
        // 检查是否有结果数据
        const results = res.data.results || [];

        // 按照 parking-list 的字段映射规范处理数据
        const searchResults = results.map(item => ({
          id: item.id,
          name: item.parkName,
          address: item.parkAddress,
          distance: formatDistance(item.distance),
          averagePrice: item.averagePrice || '0',
          availableSpots: item.availableSlots || 0,
          parkType: item.parkType ? item.parkType.desc : '普通停车场',
          image: item['image'] ? item.image : ''
        }));

        // 根据可用车位数量进行降序排序
        const sortedResults = searchResults.sort((a, b) => {
          const spotsA = parseInt(a.availableSpots) || 0;
          const spotsB = parseInt(b.availableSpots) || 0;
          return spotsB - spotsA;
        });

        this.setData({
          searchResults: sortedResults,
          loading: false
        });

        // if (sortedResults.length === 0) {
        //   this.showMessage('未找到相关停车场', 'info');
        // }
      } else {
        this.setData({
          searchResults: [],
          loading: false
        });
        this.showMessage('搜索失败，请重试', 'error');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      this.setData({
        searchResults: [],
        loading: false
      });
      this.showMessage('搜索失败，请重试', 'error');
    }
  },

  // 点击停车场项
  onParkingItemTap(e) {
    const id = e.currentTarget.dataset.id;
    const parking = this.data.searchResults.find(item => item.id === id);
    console.log(parking);
    if (!parking) {
      this.showMessage('停车场信息不存在', 'error');
      return;
    }

    // 导航到停车场详情页
    wx.navigateTo({
      url: `/pages/parking-spots/index?id=${id}`
    });
  },

  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
