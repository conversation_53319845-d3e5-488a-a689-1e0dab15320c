<t-message id="t-message" />

<view class="parking-list-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="停车场列表" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 浮动头部 -->
  <floating-header enable-collapse="{{false}}">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" >
      <!-- <view class="header-title">附近停车场</view> -->
    </view>

    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary">
      <!-- 可以根据需要添加内容 -->
    </view>
  </floating-header>

  <!-- 内容区域 -->
  <view class="parking-list-content">
    <!-- 停车场列表 -->
    <view class="parking-list">
        <!-- 只在初始加载时显示加载中 -->
        <block wx:if="{{loading && current === 1}}">
          <view class="loading-container">
            <t-loading theme="circular" size="40rpx" loading />
            <text class="loading-text">加载中...</text>
          </view>
        </block>
        <block wx:elif="{{parkingList.length === 0}}">
          <view class="empty-container">
            <t-empty icon="info-circle-filled" description="暂无停车场信息" />
          </view>
        </block>
        <!-- 始终显示列表，只是内容可能为空 -->
        <block wx:if="{{parkingList.length > 0}}">
          <view
            class="parking-item"
            wx:for="{{parkingList}}"
            wx:key="id"
            data-id="{{item.id}}"
            bindtap="onParkingTap"
          >
            <!-- 停车场卡片 -->
            <view class="parking-card">
              <!-- 左侧图片区域 -->
              <view class="parking-image-container">
                <block wx:if="{{item.image !== ''}}">
                  <image class="parking-image" src="{{item.image}}" mode="aspectFill" />
                </block>
                <block wx:else>
                  <t-icon name="location-parking-place" size="80rpx" color="#00A278" class="parking-icon" />
                </block>
              </view>

              <!-- 右侧信息区域 -->
              <view class="parking-info">
                <!-- 停车场名称和类型 -->
                <view class="parking-header">
                  <view class="parking-name">{{item.name}}</view>
                </view>

                <!-- 停车场地址 -->
                <view class="parking-address">
                  <text>{{item.address}}</text>
                </view>

                <!-- 距离信息 -->
                <view class="parking-distance">
                  <view class="distance-info">
                    <t-icon name="location" size="24rpx" color="#999999" />
                    <text>距离: {{item.distance}}</text>
                  </view>
                  <view class="parking-type">{{item.parkType}}</view>
                </view>

                <!-- 分隔线 -->
                <view class="divider"></view>

                <!-- 底部信息区域 -->
                <view class="parking-meta">
                  <!-- 余位信息 -->
                  <view class="parking-spots">
                    <text class="spots-label">余位</text>
                    <text class="spots-value">{{item.availableSpots !== null ? item.availableSpots : '未知'}}</text>
                  </view>

                  <!-- 价格信息 -->
                  <view class="parking-price">
                    <text class="price-value">{{item.averagePrice ? '￥' + item.averagePrice : '价格未知'}}</text>
                    <text class="price-unit" wx:if="{{item.averagePrice}}">\小时</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 加载更多内容 -->
        <load-more
          list-is-empty="{{!parkingList.length}}"
          status="{{loadMoreStatus}}"
          no-more-text="没有更多数据了"
          bind:retry="onRetryLoad"
        />
        </view>
  </view>
</view>
