<t-message id="t-message" />

<view class="parking-list-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="{{parkingInfo.name}}" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{80}}" id="floatingHeader">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="header-main">
      <!-- 停车场信息标签 -->
      <view class="parking-info-section" bindtap="onNavigationTap" catch:tap>
        <view class="info-tag">
          <t-icon name="location" size="28rpx" color="#ffffff" />
          <text>{{parkingInfo.address}}</text>
          <view class="navigation-icon" >
            <t-icon name="chevron-right" size="32rpx" color="#ffffff" />
          </view>
        </view>
      </view>

      <!-- 停车场元数据 -->
      <view class="parking-meta-section">
        <view class="meta-tag">
          <t-icon name="location-enlargement" size="28rpx" color="#ffffff" />
          <text>余位 {{parkingInfo.availableSpots}}</text>
        </view>
        <view class="meta-tag">
          <t-icon name="parking" size="28rpx" color="#ffffff" />
          <text>{{parkingInfo.parkType}}</text>
        </view>
        <view class="meta-tag">
          <t-icon name="money-circle" size="28rpx" color="#ffffff" />
          <text>均价  ￥{{parkingInfo.averagePrice}}/小时</text>
        </view>
      </view>
    </view>
  </floating-header>

  <view class="parking-list-content">
    <t-pull-down-refresh
      value="{{enable}}"
      bind:refresh="onRefresh"
      loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
    >
        <!-- 车位列表 -->
        <view class="spots-list">
          <block wx:if="{{loading}}">
            <view class="loading-container">
              <t-loading theme="circular" size="40rpx" loading />
              <text class="loading-text">加载中...</text>
            </view>
          </block>
          <block wx:elif="{{parkingSpots.length === 0}}">
            <view class="empty-container">
              <t-empty icon="info-circle-filled" description="暂无车位信息" />
            </view>
          </block>
          <block wx:else>
            <view
              class="spot-item {{item.status === 'busy' ? 'spot-busy' : ''}} {{!item.isAvailable ? 'spot-unavailable' : ''}}"
              wx:for="{{parkingSpots}}"
              wx:key="id"
              data-id="{{item.id}}"
              bindtap="onSpotTap"
            >
              <view class="spot-left">
                <view class="spot-image-placeholder">
                  <t-icon
                    name="parking"
                    size="40rpx"
                    color="{{!item.isAvailable ? '#CCCCCC' : (item.status === 'free' ? '#0052D9' : '#999999')}}"
                  />
                </view>
              </view>
              <view class="spot-info">
                <view class="spot-header">
                  <view class="spot-name">{{item.name}}</view>
                  <view class="status-tags">
                    <!-- 使用状态标签 -->
                    <view class="status-tag {{item.status === 'free' ? 'status-free' : 'status-busy'}}">
                      {{item.status === 'free' ? '空闲' : '使用中'}}
                    </view>
                    <!-- 可用状态标签 -->
                    <view wx:if="{{!item.isAvailable}}" class="status-tag status-unavailable">
                      不可用
                    </view>
                  </view>
                </view>

                <view class="spot-location">
                  <t-icon name="location" size="24rpx" color="#959595" />
                  <text>{{item.location}}</text>
                </view>

                <view class="spot-time">
                  <t-icon name="time" size="24rpx" color="#959595" />
                  <text>共享时段：{{item.shareTime}}</text>
                  <!-- <text wx:if="{{item.isCrossDay.code === 'YES'}}" class="cross-day-tag">跨天</text> -->
                </view>

                <!-- 添加分隔线 -->
                <view class="divider"></view>

                <view class="spot-price">￥{{item.price}}<text class="price-unit">/小时</text></view>
              </view>
              <view class="spot-actions">
                <!-- <t-icon
                  wx:if="{{item.isAvailable && item.status === 'free'}}"
                  name="lock-on"
                  size="48rpx"
                  color="#0052D9"
                  bindtap="onLockTap"
                  data-id="{{item.id}}"
                  data-name="{{item.name}}"
                  catch:tap
                />
                <t-icon
                  wx:elif="{{item.isAvailable && item.status === 'busy'}}"
                  name="lock-off"
                  size="48rpx"
                  color="#999999"
                  bindtap="onUnlockTap"
                  data-id="{{item.id}}"
                  data-name="{{item.name}}"
                  catch:tap
                /> -->
                <t-icon
                  wx:if="{{!item.isAvailable}}"
                  name="close-circle"
                  size="48rpx"
                  color="#CCCCCC"
                />
              </view>
            </view>

            <!-- 加载更多内容 -->
            <load-more
              list-is-empty="{{!parkingSpots.length}}"
              status="{{loadMoreStatus}}"
              no-more-text="没有更多车位了"
              bind:retry="onRetryLoad"
            />
          </block>
        </view>
      </t-pull-down-refresh>
    </view>
  </view>


<t-message id="t-message" />
