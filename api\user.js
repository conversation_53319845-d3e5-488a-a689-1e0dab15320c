import request from './request';

/* apiLogin 响应数据结构示例
  {
    "code": 0,
    "data": {
      "uuid": "c0fa3202-2493-4129-b1c4-a50bd7d2b4a5",
      "token": "eyJ0eXAiOiJKc29uV2ViVG9rZW4iLCJhbGciOiJIUzI1NiJ9.eyJVdWlkIjoiYzBmYTMyMDItMjQ5My00MTI5LWIxYzQtYTUwYmQ3ZDJiNGE1IiwiVXNlcklkIjoiMTkxOTU5NTIyNTMyNjA0MzEzNyIsIlBob25lIjoiMTU4NTgxNzA1MTgiLCJpYXQiOjE3NDY1MDIxNDQsIm5iZiI6MTc0NjUwMjE0NCwiZXhwIjoxNzQ3MTA2OTQ0fQ.IJB9cYwfhtp-QFmPYZzNPw5APmONaoytuZEcr0xP8XY",
      "refreshToken": null,
      "expire": "604800",
      "expiration": "2025-05-13 11:29:04"
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1746502144401",
    "errorMsg": null
  }
*/
export const apiLogin = async (payload) => {
  const { appid, code, rawData, signature, encryptedData, iv, phoneNumber } = payload;
  const loginRes = await request('/wechat/user/auth', 'POST', {
    appid,
    code,
    rawData,
    signature,
    encryptedData,
    iv,
    phoneNumber
  });
  return loginRes.data;
};

/* apiGenerateToken 响应数据结构示例
  {
    "code": 0,
    "data": {
      "uuid": "c0fa3202-2493-4129-b1c4-a50bd7d2b4a5",
      "token": "eyJ0eXAiOiJKc29uV2ViVG9rZW4iLCJhbGciOiJIUzI1NiJ9.eyJVdWlkIjoiYzBmYTMyMDItMjQ5My00MTI5LWIxYzQtYTUwYmQ3ZDJiNGE1IiwiVXNlcklkIjoiMTkxOTU5NTIyNTMyNjA0MzEzNyIsIlBob25lIjoiMTU4NTgxNzA1MTgiLCJpYXQiOjE3NDY1MDIxNDQsIm5iZiI6MTc0NjUwMjE0NCwiZXhwIjoxNzQ3MTA2OTQ0fQ.IJB9cYwfhtp-QFmPYZzNPw5APmONaoytuZEcr0xP8XY",
      "refreshToken": null,
      "expire": "604800",
      "expiration": "2025-05-13 11:29:04"
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1746502144401",
    "errorMsg": null
  }
*/
export const apiGenerateToken = async (payload) => {
  const { userId, phone } = payload;
  const tokenRes = await request('/wechat/user/token/generate', 'POST', null, {
    userId,
    phone
  });
  return tokenRes.data;
};
