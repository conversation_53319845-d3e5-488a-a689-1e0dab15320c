<t-message id="t-message" />

<view class="parking-bill-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="全部账单" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{80}}" id="floatingHeader">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="bill-main">
      <!-- 账单统计信息 -->
      <!-- <view class="bill-stats">
        <view class="stats-item">
          <view class="stats-value">{{totalOrders || 0}}</view>
          <view class="stats-label">订单总数</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-value">{{completedOrders || 0}}</view>
          <view class="stats-label">已完成</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-value">{{pendingOrders || 0}}</view>
          <view class="stats-label">待处理</view>
        </view>
      </view> -->
    </view>
  </floating-header>

  <!-- 内容区域 -->
  <view class="bill-content">
    <!-- 标签页 -->
    <t-tabs
      value="{{activeTab}}"
      bind:change="onTabChange"
      t-class="custom-tabs"
      t-class-active="custom-active-tab"
      t-class-track="custom-track"
    >
      <t-tab-panel label="全部" value="all" />
      <!-- <t-tab-panel label="进行中" value="ongoing" />
      <t-tab-panel label="已完成" value="completed" />
      <t-tab-panel label="已取消" value="canceled" />
      <t-tab-panel label="待补缴" value="pending" /> -->
    </t-tabs>

    <t-pull-down-refresh
      value="{{refreshing}}"
      loadingProps="{{loadingProps}}"
      loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
      bind:refresh="onRefresh"
    >
      <!-- 账单列表 -->
      <view class="bill-list">
        <block wx:if="{{billList.length > 0}}">
          <view
            class="bill-item"
            wx:for="{{billList}}"
            wx:key="id"
            data-id="{{item.id}}"
          >
            <!-- 车牌号和状态 -->
            <view class="bill-header">
              <view class="car-number">{{item.carNumber}}</view>
              <view class="bill-status {{item.status === 'ongoing' ? 'status-ongoing' : item.status === 'pending' ? 'status-pending' : item.status === 'canceled' ? 'status-canceled' : 'status-completed'}}">
                {{item.statusText}}
              </view>
            </view>

            <!-- 停车场信息 -->
            <view class="parking-info">
              <view class="location-dot"></view>
              <view class="parking-details">
                <view class="parking-name">{{item.parkingName}}</view>
                <view class="spot-number">{{item.spotNumber}}</view>
              </view>
            </view>

            <!-- 订单时间 -->
            <view class="time-info">
              <view class="time-label">订单时间</view>
              <view class="time-range">
                <text class="time-start">{{item.startTime || "暂无"}}</text>
                <view class="time-separator">
                  <text class="separator-text">至</text>
                </view>
                <text class="time-end">{{item.endTime || "暂无"}}</text>
              </view>
            </view>

            <!-- 停车时长和费用 -->
            <block wx:if="{{item.status !== 'ongoing'}}">
              <view class="duration-fee">
                <view class="duration-info">
                  <view class="info-label">停车时长</view>
                  <view class="info-value">{{item.duration || '暂无'}}</view>
                </view>
                <view class="fee-info">
                  <view class="info-label">停车费用</view>
                  <view class="info-value {{item.status === 'pending' ? 'pending-fee' : ''}}">￥{{item.fee}}元</view>
                </view>
              </view>

              <!-- 补缴费用 -->
              <view class="extra-fee" wx:if="{{item.status === 'pending'}}">
                <view class="info-label">补缴费用</view>
                <view class="info-value pending-fee">￥{{item.extraFee}}元</view>
              </view>
            </block>

            <!-- 操作按钮 -->
            <view class="bill-actions">
              <!-- 进行中订单显示解锁停车按钮 -->
              <block wx:if="{{item.status === 'ongoing'}}">
                <view class="action-button unlock-button" catchtap="onUnlockParking" data-id="{{item.id}}" data-lock-id="{{item.lockId}}">
                  解锁停车
                </view>
              </block>
              <!-- 非进行中订单显示查看订单按钮 -->
              <block wx:else>
                <view class="action-button" catchtap="onViewDetail" data-id="{{item.id}}">
                  查看订单
                </view>
                <view class="action-button payment-button" wx:if="{{item.status === 'pending'}}" catchtap="onViewDetail" data-id="{{item.id}}">
                  支付补缴
                </view>
              </block>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <block wx:else>
          <t-empty
            icon="info-circle-filled"
            description="{{activeTab === 'all' ? '暂无账单记录' : '暂无' + (activeTab === 'ongoing' ? '进行中' : activeTab === 'completed' ? '已完成' : activeTab === 'canceled' ? '已取消' : '待补缴') + '的账单记录'}}"
            wx:if="{{!loading}}"
          />
        </block>
      </view>

      <!-- 加载状态 -->
      <view class="loading-wrapper" wx:if="{{loading}}">
        <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
      </view>

      <!-- 加载完成提示 -->
      <view class="no-more" wx:if="{{billList.length > 0 && !hasMoreData && !loading}}">
        没有更多数据了
      </view>
    </t-pull-down-refresh>
  </view>
</view>
