@import '/variable.less';

.car-management-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;

  .car-management-content {
    padding: 24rpx 24rpx 120rpx;
    position: relative;
    z-index: 1;
  }

  /* 浮动头部样式 */
  .header-main {
    padding: 20rpx 0;

    .header-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
    }
  }

  .header-secondary {
    padding: 20rpx 0;

    .header-subtitle {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }
  }
}

/* 车牌列表样式 */
.plate-list {
  margin-top: 20rpx;
}

/* 车牌卡片样式 */
.plate-item {
  margin-bottom: 24rpx;
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.plate-card {
  display: flex;
  align-items: center;
  background-color: @bg-color-white;
  padding: 24rpx;
  position: relative;
}

/* 车牌图标容器 */
.plate-icon-container {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 82, 217, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 车牌信息区域 */
.plate-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plate-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.plate-number {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
  letter-spacing: 2rpx;
}

.plate-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #959595;
}

.plate-color {
  margin-right: 16rpx;
}

.plate-time {
  font-size: 24rpx;
  color: #CCCCCC;
}

/* 右侧操作区域 */
.plate-actions {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}

/* 滑动操作样式 */
.swipe-actions {
  display: flex;
  height: 100%;
}

.delete-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #FA5151;
  color: #FFFFFF;
  padding: 0 40rpx;
  font-size: 24rpx;
  min-width: 120rpx;

  text {
    margin-top: 8rpx;
  }
}

/* 添加车牌区域 */
.add-plate-section {
  margin-top: 40rpx;
  padding: 0 24rpx;
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: @bg-color-white;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #999999;
}

.empty-title {
  margin-top: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: @gy1;
}

.empty-subtitle {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #959595;
  text-align: center;
  line-height: 1.5;
}

/* 添加车牌弹窗样式 */
.add-plate-dialog {
  width: 600rpx;
  background-color: @bg-color-white;
  border-radius: 24rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
}

.dialog-content {
  padding: 32rpx;
}

.plate-format-hint {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
  padding: 16rpx 20rpx;
  background-color: #F7F8FA;
  border-radius: 12rpx;

  t-icon {
    margin-right: 8rpx;
  }
}

.dialog-footer {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
}
