<t-navbar title="数据中心" left-arrow />
<view class="data-center">
  <view class="card first">
    <view class="card-header">
      <view class="card-header__title"> 整体情况 </view>
      <view class="card-header__desc"> 截止12:00累计数值 </view>
    </view>
    <view class="card-content">
      <view class="card-content__item" wx:for="{{totalSituationDataList}}" wx:for-item="item" wx:key="index">
        <view class="card-content__item--desc"> {{item.name}} </view>
        <view class="card-content__item--count"> {{item.number}} </view>
      </view>
    </view>
  </view>

  <view class="card second">
    <view class="card-header">
      <view class="card-header__title"> 互动情况 </view>
    </view>
    <view class="card-content">
      <view class="card-content__item" wx:for="{{interactionSituationDataList}}" wx:for-item="item" wx:key="*this">
        <view class="card-content__item--desc"> {{item.name}} </view>
        <view class="card-content__item--count">
          <text>{{item.number}}</text>
          <text class="unit">次</text>
        </view>
      </view>
    </view>
  </view>

  <view class="card third">
    <view class="card-header">
      <view class="card-header__title"> 完播率 </view>
    </view>
    <view class="card-content">
      <view class="card-content__item" wx:for="{{completeRateDataList}}" wx:for-item="item" wx:key="index">
        <text class="progress__label"> {{item.time}} </text>
        <view class="progress__content">
          <t-progress percentage="{{item.percentage}}" />
        </view>
      </view>
    </view>
  </view>

  <view class="card forth">
    <view class="card-header">
      <view class="card-header__title"> 按区域统计 </view>
    </view>
    <view class="card-content">
      <t-empty icon="info-circle-filled" description="Empty Data" />
    </view>
  </view>
</view>
