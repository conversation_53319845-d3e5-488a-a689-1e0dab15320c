Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回图标
    showBackIcon: {
      type: Boolean,
      value: true
    },
    // 是否显示渐变背景
    background: {
      type: Boolean,
      value: true
    },
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 返回的页面层数
    delta: {
      type: Number,
      value: 1
    },
    // 是否使用自定义返回逻辑
    useCustomBack: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 返回按钮点击事件
    onBackTap() {
      // 先触发自定义事件，允许页面自行处理返回逻辑
      const eventDetail = { delta: this.data.delta };
      this.triggerEvent('back', eventDetail);

      // 如果使用自定义返回逻辑，则不执行默认的返回行为
      if (this.data.useCustomBack) {
        return;
      }

      // 获取当前页面栈
      const pages = getCurrentPages();
      console.log('Current pages stack:', pages, 'Delta:', this.data.delta);

      // 如果页面栈中的页面数量大于要返回的层数，则返回指定层数
      if (pages.length > this.data.delta) {
        wx.navigateBack({
          delta: this.data.delta,
          fail: (err) => {
            console.error('Failed to navigate back:', err);
            // 如果返回失败，尝试使用路由跳转到首页
            wx.switchTab({
              url: '/pages/index/index',
              fail: (switchErr) => {
                console.error('Failed to switch to home page:', switchErr);
              }
            });
          }
        });
      } else {
        // 如果页面栈中的页面数量不足，则跳转到首页
        wx.switchTab({
          url: '/pages/index/index',
          fail: (err) => {
            console.error('Failed to switch to home page:', err);
            // 如果跳转到首页失败，尝试返回到可能的上一页
            if (pages.length > 1) {
              wx.navigateBack({ delta: 1 });
            }
          }
        });
      }
    }
  }
})
