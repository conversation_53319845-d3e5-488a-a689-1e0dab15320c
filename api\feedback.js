import request from './request';

/* apiCreateFeedback 接口说明
  请求参数:
  {
    "type": "DEDUCTION_ISSUE",      // 类型: DEDUCTION_ISSUE-扣款异常, LOCK_OPERATION_ISSUE-开关锁异常, DEPOSIT_NOT_REFUNDED-保证金未退还, PAGE_LOADING_FAILURE-页面加载失败, OTHER-其他
    "remark": "点击关锁，没有变化",   // 备注
    "phone": "18575647898"          // 电话号
  }

  响应数据结构示例:
  {
    "code": 0,
    "data": null,
    "msg": "成功",
    "path": null,
    "timestamp": "1747594273554",
    "errorMsg": null
  }
*/
export const apiCreateFeedback = async (payload) => {
  const { type, remark, phone } = payload;
  const feedbackRes = await request('/mobile/feedback/create', 'POST', {
    type,
    remark,
    phone
  });
  return feedbackRes.data;
};
