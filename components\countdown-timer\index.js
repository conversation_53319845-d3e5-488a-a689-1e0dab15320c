Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 结束时间字符串
    endTime: {
      type: String,
      value: ''
    },
    // 自动开始
    autoStart: {
      type: Boolean,
      value: true
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义类名
    customClass: {
      type: String,
      value: ''
    },
    // 显示格式：'hms' (时:分:秒) 或 'text' (文本格式)
    format: {
      type: String,
      value: 'hms'
    },
    // 超时显示文本
    timeoutText: {
      type: String,
      value: '已超时'
    },
    // 警告时间阈值（秒）
    warningThresholds: {
      type: Array,
      value: [30 * 60, 10 * 60, 5 * 60] // 30分钟、10分钟、5分钟
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 倒计时状态
    countdown: {
      hours: '00',
      minutes: '00',
      seconds: '00'
    },
    // 是否激活倒计时
    isActive: false,
    // 是否已超时
    isTimeout: false,
    // 定时器ID
    intervalId: null,
    // 剩余总秒数
    remainingSeconds: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
      if (this.properties.autoStart && this.properties.endTime) {
        this.startCountdown();
      }
    },

    detached() {
      // 清理定时器
      this.clearTimer();
    }
  },

  /**
   * 属性监听器
   */
  observers: {
    'endTime': function(newEndTime) {
      if (newEndTime) {
        this.startCountdown();
      } else {
        this.stopCountdown();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 启动倒计时
     */
    startCountdown() {
      const endTimeStr = this.properties.endTime;
      
      if (!endTimeStr) {
        console.warn('countdown-timer: endTime is required');
        return;
      }

      // 清除可能存在的旧定时器
      this.clearTimer();

      // 解析结束时间
      const endTime = this.parseEndTime(endTimeStr);
      if (!endTime) {
        console.error('countdown-timer: Invalid endTime format');
        return;
      }

      // 计算初始剩余时间
      const now = new Date();
      const remainingTime = endTime.getTime() - now.getTime();

      // 检查是否已超时
      if (remainingTime <= 0) {
        this.handleTimeout();
        return;
      }

      // 设置为激活状态
      this.setData({ 
        isActive: true, 
        isTimeout: false 
      });

      // 立即更新一次显示
      this.updateDisplay(Math.floor(remainingTime / 1000));

      // 创建定时器
      const intervalId = setInterval(() => {
        this.tick(endTime);
      }, 1000);

      this.setData({ intervalId });
    },

    /**
     * 停止倒计时
     */
    stopCountdown() {
      this.clearTimer();
      this.setData({ 
        isActive: false,
        isTimeout: false,
        countdown: {
          hours: '00',
          minutes: '00',
          seconds: '00'
        }
      });
    },

    /**
     * 暂停倒计时
     */
    pauseCountdown() {
      this.clearTimer();
      this.setData({ isActive: false });
    },

    /**
     * 恢复倒计时
     */
    resumeCountdown() {
      if (this.properties.endTime && !this.data.isTimeout) {
        this.startCountdown();
      }
    },

    /**
     * 定时器tick
     */
    tick(endTime) {
      const now = new Date();
      const remainingTime = endTime.getTime() - now.getTime();

      if (remainingTime <= 0) {
        this.handleTimeout();
        return;
      }

      const remainingSeconds = Math.floor(remainingTime / 1000);
      this.updateDisplay(remainingSeconds);
      this.checkWarnings(remainingSeconds);
    },

    /**
     * 更新显示
     */
    updateDisplay(totalSeconds) {
      if (totalSeconds <= 0) {
        this.setData({
          countdown: {
            hours: '00',
            minutes: '00',
            seconds: '00'
          },
          remainingSeconds: 0
        });
        return;
      }

      // 计算时、分、秒
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      // 格式化显示
      this.setData({
        countdown: {
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0')
        },
        remainingSeconds: totalSeconds
      });

      // 触发变化事件
      this.triggerEvent('change', {
        hours,
        minutes,
        seconds,
        totalSeconds,
        formattedTime: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      });
    },

    /**
     * 处理超时
     */
    handleTimeout() {
      this.clearTimer();
      this.setData({ 
        isActive: false, 
        isTimeout: true,
        countdown: {
          hours: '00',
          minutes: '00',
          seconds: '00'
        }
      });

      // 触发超时事件
      this.triggerEvent('timeout');
      this.triggerEvent('finish'); // 兼容性事件
    },

    /**
     * 检查警告
     */
    checkWarnings(remainingSeconds) {
      const thresholds = this.properties.warningThresholds;
      
      for (let threshold of thresholds) {
        if (remainingSeconds === threshold) {
          this.triggerEvent('warning', {
            remainingSeconds,
            threshold,
            message: this.getWarningMessage(threshold)
          });
          break;
        }
      }
    },

    /**
     * 获取警告消息
     */
    getWarningMessage(threshold) {
      const minutes = Math.floor(threshold / 60);
      if (minutes >= 60) {
        const hours = Math.floor(minutes / 60);
        return `共享时间还剩${hours}小时，请注意及时处理`;
      } else if (minutes >= 10) {
        return `共享时间还剩${minutes}分钟，请尽快处理`;
      } else {
        return `共享时间还剩${minutes}分钟，请立即处理`;
      }
    },

    /**
     * 解析结束时间
     */
    parseEndTime(endTimeStr) {
      try {
        let endTime;
        if (endTimeStr.includes('T')) {
          // ISO格式: 2023-05-10T15:30:00
          endTime = new Date(endTimeStr);
        } else {
          // 普通格式: 2023-05-10 15:30:00
          endTime = new Date(endTimeStr.replace(/-/g, '/'));
        }

        // 验证日期是否有效
        if (isNaN(endTime.getTime())) {
          return null;
        }

        return endTime;
      } catch (error) {
        console.error('countdown-timer: Failed to parse endTime', error);
        return null;
      }
    },

    /**
     * 清除定时器
     */
    clearTimer() {
      if (this.data.intervalId) {
        clearInterval(this.data.intervalId);
        this.setData({ intervalId: null });
      }
    },

    /**
     * 获取当前状态
     */
    getStatus() {
      return {
        isActive: this.data.isActive,
        isTimeout: this.data.isTimeout,
        remainingSeconds: this.data.remainingSeconds,
        countdown: this.data.countdown
      };
    }
  }
})
