@import '/variable.less';

/* 主容器样式 */
.parking-bill-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  box-sizing: border-box;

  /* 账单主要内容 */
  .bill-main {
    padding: 20rpx 30rpx 0; /* 增加顶部内边距，与导航栏保持适当距离 */
    text-align: center;
  }

  /* 账单次要内容 */
  .bill-secondary {
    width: 100%;
    overflow: hidden; /* 确保内容不溢出 */
  }

  /* 账单摘要 */
  .bill-summary {
    margin-top: 20rpx; /* 增加顶部外边距，与导航栏保持适当距离 */

    .summary-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
      margin-bottom: 8rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    .summary-subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  /* 账单统计信息 */
  .bill-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16rpx;
    padding: 24rpx 0;
    margin: 30rpx 30rpx 0; /* 调整顶部外边距，与摘要保持适当距离 */
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;

    .stats-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .stats-value {
        font-size: 32rpx;
        font-weight: 500;
        color: #FFFFFF;
        margin-bottom: 8rpx;
      }

      .stats-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .stats-divider {
      width: 2rpx;
      height: 50rpx;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  /* 内容区域 */
  .bill-content {
    // padding: 40rpx 0 0;
    // margin-top: 40rpx; /* 增加顶部外边距，与头部保持适当距离 */

    /* 自定义标签页样式 */
    .custom-tabs {
      background-color: #FFFFFF;
      margin-bottom: 20rpx;
      --td-tab-item-color: #666666;
      --td-tab-track-color: @brand7-normal;
      --td-tab-item-active-color: @brand7-normal;
      --td-tab-item-font-size: 28rpx;
    }

    .custom-active-tab {
      font-weight: 500;
    }

    .custom-track {
      height: 4rpx !important;
      border-radius: 4rpx !important;
      bottom: 0 !important;
    }

    /* 账单列表 */
    .bill-list {
      padding: 0 24rpx;
    }

    /* 账单项 */
    .bill-item {
      background-color: #FFFFFF;
      border-radius: 12rpx;
      padding: 30rpx; /* 增加内边距 */
      margin-bottom: 24rpx; /* 增加外边距 */
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      position: relative;

      &:active {
        background-color: #F9F9F9;
      }

      /* 账单头部 */
      .bill-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx; /* 增加底部间距 */
        padding-bottom: 16rpx; /* 添加底部内边距 */
        border-bottom: 1rpx solid #F0F0F0; /* 添加底部边框 */

        .car-number {
          font-size: 34rpx; /* 增大字体 */
          font-weight: 600; /* 加粗 */
          color: @gy1;
        }

        .bill-status {
          font-size: 24rpx;
          padding: 6rpx 18rpx; /* 增加内边距 */
          border-radius: 26rpx;
          border: 2rpx solid;

          &.status-ongoing {
            color: @brand7-normal;
            border-color: @brand7-normal;
          }

          &.status-completed {
            color: @brand7-normal;
            border-color: @brand7-normal;
          }

          &.status-canceled {
            color: @brand7-normal;
            border-color: @brand7-normal;
          }

          &.status-pending {
            color: #FA7542;
            border-color: #FA7542;
          }
        }
      }

      /* 停车场信息 */
      .parking-info {
        display: flex;
        align-items: flex-start;
        margin-bottom: 24rpx; /* 增加底部间距 */

        .location-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: @brand7-normal;
          margin-top: 16rpx;
          margin-right: 16rpx;
          flex-shrink: 0; /* 防止压缩 */
        }

        .parking-details {
          flex: 1;

          .parking-name {
            font-size: 30rpx;
            color: @gy2;
            margin-bottom: 10rpx; /* 增加底部间距 */
            line-height: 1.4;
            font-weight: 500; /* 加粗 */
          }

          .spot-number {
            font-size: 26rpx;
            color: @gy2;
            display: flex; /* 使用弹性布局 */
            align-items: center; /* 垂直居中 */
          }
        }
      }

      /* 订单时间 */
      .time-info {
        background-color: #F7F8FA;
        border-radius: 12rpx; /* 增加圆角 */
        padding: 24rpx; /* 增加内边距 */
        margin-bottom: 24rpx; /* 增加底部间距 */
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03); /* 添加轻微阴影 */

        .time-label {
          font-size: 24rpx;
          color: @gy2;
          margin-bottom: 12rpx;
          font-weight: 500;
        }

        .time-range {
          display: flex;
          align-items: center;
          justify-content: space-between; /* 两端对齐 */

          .time-start, .time-end {
            font-size: 28rpx; /* 增大字体 */
            color: @gy1;
            font-weight: 500; /* 加粗 */
            flex: 1; /* 占据可用空间 */
          }

          .time-start {
            text-align: left; /* 左对齐 */
          }

          .time-end {
            text-align: right; /* 右对齐 */
          }

          .time-separator {
            display: flex;
            align-items: center;
            margin: 0 20rpx; /* 增加间距 */
            flex-shrink: 0; /* 防止压缩 */

            .line-icon {
              width: 60rpx; /* 增加宽度 */
              height: 4rpx;
            }

            .separator-text {
              font-size: 24rpx;
              color: @brand7-normal;
              margin: 0 8rpx;
            }
          }
        }
      }

      /* 停车时长和费用 */
      .duration-fee {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx; /* 增加底部间距 */
        padding: 24rpx; /* 增加内边距 */
        background-color: #F7F8FA; /* 添加背景色 */
        border-radius: 12rpx; /* 增加圆角 */
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03); /* 添加轻微阴影 */

        .duration-info, .fee-info {
          flex: 1; /* 占据可用空间 */

          .info-label {
            font-size: 24rpx;
            color: @gy2;
            margin-bottom: 8rpx; /* 增加间距 */
            font-weight: 500; /* 加粗 */
          }

          .info-value {
            font-size: 28rpx; /* 增大字体 */
            color: @gy1;
            font-weight: 500; /* 加粗 */

            &.pending-fee {
              color: #FA7542;
              font-weight: 600; /* 更加突出 */
            }
          }
        }

        .duration-info {
          text-align: left; /* 左对齐 */
          margin-right: 20rpx; /* 右侧间距 */
        }

        .fee-info {
          text-align: right; /* 右对齐 */
        }
      }

      /* 补缴费用 */
      .extra-fee {
        margin-bottom: 24rpx; /* 增加底部间距 */
        padding: 24rpx; /* 增加内边距 */
        text-align: right; /* 右对齐 */
        background-color: #FFF7F5; /* 添加淡橙色背景 */
        border-radius: 12rpx; /* 增加圆角 */
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03); /* 添加轻微阴影 */

        .info-label {
          font-size: 24rpx;
          color: @gy2;
          margin-bottom: 8rpx; /* 增加间距 */
          font-weight: 500; /* 加粗 */
        }

        .info-value {
          font-size: 28rpx; /* 增大字体 */
          font-weight: 600; /* 加粗 */

          &.pending-fee {
            color: #FA7542;
          }
        }
      }

      /* 操作按钮 */
      .bill-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 30rpx; /* 增加顶部间距 */
        padding-top: 20rpx; /* 添加顶部内边距 */
        border-top: 1rpx solid #F0F0F0; /* 添加顶部边框 */

        .action-button {
          height: 70rpx; /* 增加高度 */
          padding: 0 36rpx; /* 增加内边距 */
          background-color: @brand7-normal;
          color: #FFFFFF;
          font-size: 28rpx; /* 增大字体 */
          font-weight: 500; /* 加粗 */
          border-radius: 35rpx; /* 增加圆角 */
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 24rpx; /* 增加间距 */
          box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.15); /* 添加阴影 */

          &.payment-button {
            background-color: #FA7542;
            box-shadow: 0 4rpx 8rpx rgba(250, 117, 66, 0.2); /* 添加橙色阴影 */
            font-weight: 600; /* 更加加粗 */
          }

          &.unlock-button {
            background-color: @brand-contrast-green;
            box-shadow: 0 4rpx 8rpx rgba(0, 162, 120, 0.2); /* 添加绿色阴影 */
            font-weight: 600; /* 更加加粗 */
          }

          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }

  /* 加载状态 */
  .loading-wrapper {
    padding: 30rpx 0;
    text-align: center;
    color: @gy2;
  }

  /* 没有更多数据提示 */
  .no-more {
    text-align: center;
    padding: 30rpx 0;
    font-size: 26rpx;
    color: #999999;
  }
}
