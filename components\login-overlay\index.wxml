<t-overlay
  visible="{{visible && !isLoggedIn}}"
  bind:click="handleOverlayClick">
  <view class="login-overlay-content">
    <!-- 登录内容 -->
    <view class="login-normal">
      <view class="login-overlay-title">请先登录</view>
      <view class="login-overlay-desc">快捷登录后即可使用全部功能</view>

      <!-- 显示错误消息（如果有） -->
      <view wx:if="{{errorMessage}}" class="login-error-message">
        <t-icon name="error-circle-filled" size="32rpx" />
        <text>{{errorMessage}}</text>
      </view>

      <t-button
        id="agree-privacy-login-btn"
        theme="primary"
        size="large"
        class="login-button"
        bindgetphonenumber="{{'login'}}"
        open-type="{{'getPhoneNumber'}}"
      >{{'一键快捷登录'}}</t-button>

      <!-- <view class="login-mock-container">
        <t-button
          theme="default"
          size="large"
          class="login-mock-button"
          catch:tap="loginMock"
        >模拟登录</t-button>
      </view> -->
    </view>
  </view>
</t-overlay>