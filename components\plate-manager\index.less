/* components/plate-manager/index.less */
@import '../../variable.less';

.plate-manager {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #F0F0F0;
  }
  
  &__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
  
  &__content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0 30rpx;
    position: relative;
    min-height: 200rpx;
  }
  
  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  &__footer {
    padding: 30rpx;
    border-top: 1rpx solid #F0F0F0;
  }
}

.plate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F0F0F0;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    .plate-text {
      color: @brand7-normal;
      font-weight: 500;
    }
  }
}

.plate-info {
  display: flex;
  align-items: center;
}

.plate-text {
  font-size: 32rpx;
  color: #333;
}

.default-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background-color: @brand7-normal;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  line-height: 1;
  font-weight: 400;
}

.plate-actions {
  display: flex;
  align-items: center;
  
  t-icon {
    margin-left: 20rpx;
    padding: 10rpx;
  }
}

.empty-plate-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
  }
}

.add-plate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  color: @brand7-normal;
  font-size: 28rpx;
  
  text {
    margin-left: 8rpx;
  }
}

.add-plate-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #F0F0F0;
  }
  
  &__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
  
  &__content {
    padding: 30rpx;
  }
  
  &__footer {
    padding: 30rpx;
    border-top: 1rpx solid #F0F0F0;
  }
}

.plate-format-hint {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
  
  text {
    margin-left: 8rpx;
  }
}
