@import '/variable.less';

/* 主容器样式 */
.parking-info-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;
}

/* 停车场信息主要内容 */
.parking-info-main {
  padding: 0 30rpx;
  text-align: center;
}

/* 停车场名称 */
.parking-name {
  font-size: 44rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 停车场地址 */
.parking-address {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  
  t-icon {
    margin-right: 8rpx;
  }
}

/* 停车场照片 */
.parking-photo {
  margin: 24rpx 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  height: 300rpx;
}

.photo-image {
  width: 100%;
  height: 100%;
}

/* 开放时间和停车费用 */
.opening-hours, .parking-fee {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 30rpx;
  margin-bottom: 16rpx;
}

.info-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  
  t-icon {
    margin-right: 8rpx;
  }
}

.info-value {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 内容区域 */
.parking-info-content {
  padding: 50rpx 24rpx 100rpx;
  position: relative;
  z-index: 1;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 36rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: @gy1;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 24rpx;
    background-color: @brand7-normal;
    border-radius: 3rpx;
  }
}

/* 车位列表 */
.spot-list {
  
}

.spot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
  
  &:last-child {
    border-bottom: none;
  }
}

.spot-info {
  flex: 1;
}

.spot-number {
  font-size: 30rpx;
  font-weight: 500;
  color: @gy1;
  margin-bottom: 8rpx;
}

.spot-type {
  font-size: 26rpx;
  color: @gy2;
}

.spot-price {
  font-size: 30rpx;
  color: @brand7-normal;
  font-weight: 500;
  margin-right: 24rpx;
}

.book-button {
  width: 120rpx !important;
  height: 64rpx !important;
  font-size: 26rpx !important;
  border-radius: 32rpx !important;
}

/* 设施服务 */
.facility-list {
  display: flex;
  flex-wrap: wrap;
}

.facility-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  margin-bottom: 24rpx;
  
  t-icon {
    margin-bottom: 8rpx;
  }
  
  text {
    font-size: 24rpx;
    color: @gy2;
  }
}

/* 用户评价 */
.rating-overview {
  display: flex;
  align-items: center;
}

.rating-score {
  font-size: 48rpx;
  font-weight: 600;
  color: @gy1;
  margin-right: 24rpx;
}

.rating-stars {
  display: flex;
  margin-right: 16rpx;
}

.rating-count {
  font-size: 26rpx;
  color: @gy2;
}
