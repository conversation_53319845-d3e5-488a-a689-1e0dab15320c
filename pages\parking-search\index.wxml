<t-navbar title="搜索" left-arrow bind:go-back="onSearchCancel" />
<view class="page search-park-container">
  <!-- 搜索框 -->
  <view class="search-input">
    <t-search
      t-class-input-container="t-class__input-container"
      t-class-input="t-search__input"
      value="{{searchValue}}"
      leftIcon="search"
      action="取消"
      shape="round"
      placeholder="输入目的地查找车位"
      bind:submit="onSearchSubmit"
      bind:action-click="onSearchCancel"
      focus
    ></t-search>
  </view>

  <!-- 搜索结果列表 -->
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="parking-list">
      <view
        class="parking-item"
        wx:for="{{searchResults}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onParkingItemTap"
      >
        <!-- 停车场卡片 -->
        <view class="parking-card">
          <!-- 左侧图片区域 -->
          <view class="parking-image-container">
            <block wx:if="{{item.image !== ''}}">
              <image class="parking-image" src="{{item.image}}" mode="aspectFill" />
            </block>
            <block wx:else>
              <t-icon name="location-parking-place" size="80rpx" color="#00A278" class="parking-icon" />
            </block>
          </view>

          <!-- 右侧信息区域 -->
          <view class="parking-info">
            <!-- 停车场名称和类型 -->
            <view class="parking-header">
              <view class="parking-name">{{item.name}}</view>
            </view>

            <!-- 停车场地址 -->
            <view class="parking-address">
              <text>{{item.address}}</text>
            </view>

            <!-- 距离信息 -->
            <view class="parking-distance">
              <view class="distance-info">
                <t-icon name="location" size="24rpx" color="#999999" />
                <text>距离: {{item.distance}}</text>
              </view>
              <view class="parking-type">{{item.parkType}}</view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 底部信息区域 -->
            <view class="parking-meta">
              <!-- 余位信息 -->
              <view class="parking-spots">
                <text class="spots-label">余位</text>
                <text class="spots-value">{{item.availableSpots !== null ? item.availableSpots : '未知'}}</text>
              </view>

              <!-- 价格信息 -->
              <view class="parking-price">
                <text class="price-value">{{item.averagePrice ? '￥' + item.averagePrice : '价格未知'}}</text>
                <text class="price-unit" wx:if="{{item.averagePrice}}">\小时</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-wrap" wx:elif="{{!searchValue && historyWords.length > 0}}">
    <view class="search-header">
      <text class="search-title">历史记录</text>
      <t-icon name="delete" size="large" class="search-clear" bind:click="handleClearHistory" />
    </view>
    <view class="search-content">
      <view
        class="search-item"
        hover-class="hover-history-item"
        wx:for="{{historyWords}}"
        bind:tap="handleHistoryTap"
        data-index="{{index}}"
        wx:key="*this"
      >
        <t-tag class="history-item margin-12" max-width="{{343}}" variant="light">{{item}}</t-tag>
      </view>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="popular-wrap" wx:elif="{{!searchValue && popularWords.length > 0}}">
    <view class="search-header">
      <text class="search-title">热门搜索</text>
    </view>
    <view class="search-content">
      <view
        class="search-item"
        hover-class="hover-history-item"
        wx:for="{{popularWords}}"
        bind:tap="handlePopularTap"
        data-index="{{index}}"
        wx:key="*this"
      >
        <t-tag class="popular-item margin-12" max-width="{{343}}" variant="light" icon="search">{{item}}</t-tag>
      </view>
    </view>
  </view>

  <!-- 无搜索结果 -->
  <view class="empty-result" wx:elif="{{searchValue && !loading && searchResults.length === 0}}">
    <t-empty icon="search" description="未找到相关停车场" />
  </view>

  <!-- 加载中 -->
  <t-loading class="loading-center" wx:if="{{loading}}" theme="circular" size="40rpx" loading />

  <t-message id="t-message" />
  <t-dialog
    visible="{{dialogShow}}"
    content="{{dialog.message}}"
    bindconfirm="confirm"
    bind:close="close"
    confirm-btn="确定"
    cancel-btn="{{dialog.showCancelButton ? '取消' : null}}"
  />
</view>
