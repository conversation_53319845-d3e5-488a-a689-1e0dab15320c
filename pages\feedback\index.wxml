<!-- pages/feedback/index.wxml -->
<view class="feedback-container">
  <!-- 导航栏 -->
  <gradient-navbar title="意见反馈" bind:back="onBack" />

  <!-- 浮动头部 - 仅用于保持页面结构一致性，不包含重复标题 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{100}}">
    <view slot="main" class="feedback-header">
      <!-- 移除重复标题 -->
    </view>
  </floating-header>

  <!-- 主要内容区域 -->
  <view class="feedback-content">
    <!-- 统一的表单盒子 -->
    <view class="feedback-form-box">
      <!-- 问题类型选择 -->
      <view class="form-section">
        <view class="section-title">选择您要反馈的问题类型</view>
        <t-radio-group value="{{formData.problemType}}" bind:change="onProblemTypeChange">
          <view class="problem-types">
            <view
              wx:for="{{problemTypes}}"
              wx:key="value"
              class="problem-type-item {{formData.problemType === item.value ? 'problem-type-item--active' : ''}}"
              bindtap="onProblemTypeChange"
              data-value="{{item.value}}"
            >
              {{item.label}}
            </view>
          </view>
        </t-radio-group>
      </view>

      <view class="form-divider"></view>

      <!-- 问题描述 -->
      <view class="form-section">
        <view class="section-title">请详细描述您的建议和感想</view>
        <t-textarea
          value="{{formData.description}}"
          placeholder="此处输入您要反馈的问题（20字以内）"
          maxlength="{{maxLength}}"
          disableDefaultPadding="{{true}}"
          indicator
          autosize
          bind:change="onDescriptionChange"
          t-class="feedback-textarea"
        />
      </view>
      <view class="form-divider"></view>

      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">您的联系方式</view>
        <t-input
          value="{{formData.contact}}"
          placeholder="此处输入您的电话"
          type="number"
          maxlength="11"
          bind:change="onContactChange"
          t-class="feedback-input"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="feedback-submit">
      <t-button
        theme="primary"
        size="large"
        block
        bind:tap="submitFeedback"
        t-class="submit-button"
      >
        提交反馈
      </t-button>
    </view>
  </view>
</view>

<!-- 消息提示 -->
<t-message id="t-message" />
