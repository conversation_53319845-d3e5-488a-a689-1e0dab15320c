// pages/plate-management/index.js
import { apiCreateLicensePlate, apiDeleteLicensePlate, apiGetLicensePlateList, apiSetDefaultLicensePlate } from '../../api/plate';
import Message from 'tdesign-miniprogram/message';

Page({
  data: {
    plateList: [],
    loading: false,
    showAddDialog: false,
    newPlate: '',
    editingPlate: null,
    showEditDialog: false,
    editPlateValue: ''
  },

  onLoad() {
    // 页面加载时获取车牌列表
    this.fetchPlateList();
  },

  onShow() {
    // 页面显示时刷新车牌列表
    this.fetchPlateList();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.fetchPlateList().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 获取车牌列表
  async fetchPlateList() {
    try {
      this.setData({ loading: true });

      const res = await apiGetLicensePlateList();
      console.log('获取车牌列表API响应:', res);

      if (res && res.code === 0 && res.data) {
        // 处理车牌列表数据 - 数据在data.results中
        const plates = res.data.results || res.data || [];

        if (Array.isArray(plates)) {
          console.log('处理车牌数据:', plates);
          // 提取车牌号列表
          const plateNumbers = [];

          // 遍历车牌列表
          plates.forEach(plate => {
            plateNumbers.push({
              id: plate.id,
              plateNo: plate.plateNo,
              plateColor: plate.plateColor,
              isDefault: plate.isDefault && plate.isDefault.code === 'YES',
              createdTime: this.formatTime(plate.createdTime)
            });
          });

          console.log('处理后的车牌列表:', plateNumbers);

          this.setData({
            plateList: plateNumbers,
            loading: false
          });
        } else {
          this.setData({ plateList: [], loading: false });
          this.showMessage('车牌列表数据格式错误');
        }
      } else {
        this.setData({ plateList: [], loading: false });
        this.showMessage('获取车牌列表失败');
      }
    } catch (error) {
      console.error('获取车牌列表错误:', error);
      this.setData({ plateList: [], loading: false });
      this.showMessage('获取车牌列表失败');
    }
  },

  // 显示添加车牌弹窗
  showAddDialog() {
    this.setData({
      showAddDialog: true,
      newPlate: ''
    });
  },

  // 关闭添加车牌弹窗
  closeAddDialog() {
    this.setData({
      showAddDialog: false,
      newPlate: ''
    });
  },

  // 输入车牌号
  onPlateInput(e) {
    this.setData({
      newPlate: e.detail.value.toUpperCase()
    });
  },

  // 添加新车牌
  async addNewPlate() {
    const { newPlate, plateList } = this.data;

    if (!newPlate) {
      this.showMessage('请输入车牌号');
      return;
    }

    // 简单的车牌号验证
    const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5,6}$/;
    if (!plateRegex.test(newPlate)) {
      this.showMessage('请输入正确的车牌号');
      return;
    }

    // 检查是否已存在
    const plateExists = plateList.some(plate => plate.plateNo === newPlate);
    if (plateExists) {
      this.showMessage('该车牌已存在');
      return;
    }

    try {
      this.setData({ loading: true });

      // 调用添加车牌API
      const res = await apiCreateLicensePlate({
        plateNo: newPlate,
        isDefault: plateList.length === 0 ? 1 : 0 // 如果是第一个车牌，设为默认
      });

      console.log('apiCreateLicensePlate', res);
      if (res && res.code === 0) {
        // 添加成功，刷新车牌列表
        this.showMessage('添加车牌成功', 'success');
        this.setData({
          showAddDialog: false,
          newPlate: ''
        });

        // 重新获取车牌列表
        await this.fetchPlateList();
      } else {
        this.setData({ loading: false });
        const errorMsg = res && res.msg ? res.msg : '添加车牌失败';
        this.showMessage(errorMsg);
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('添加车牌错误:', error);
      this.showMessage('添加车牌失败，请重试');
    }
  },

  // 删除车牌
  async deletePlate(e) {
    const plate = e.currentTarget.dataset.plate;

    wx.showModal({
      title: '删除车牌',
      content: `确定要删除车牌 ${plate.plateNo} 吗？`,
      confirmColor: '#0052D9',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ loading: true });

            const deleteRes = await apiDeleteLicensePlate({ id: plate.id });

            if (deleteRes && deleteRes.code === 0) {
              this.showMessage('删除车牌成功', 'success');
              await this.fetchPlateList();
            } else {
              this.setData({ loading: false });
              const errorMsg = deleteRes && deleteRes.msg ? deleteRes.msg : '删除车牌失败';
              this.showMessage(errorMsg);
            }
          } catch (error) {
            this.setData({ loading: false });
            console.error('删除车牌错误:', error);
            this.showMessage('删除车牌失败，请重试');
          }
        }
      }
    });
  },

  // 设置默认车牌
  async setDefaultPlate(e) {
    const plate = e.currentTarget.dataset.plate;

    if (plate.isDefault) {
      this.showMessage('该车牌已是默认车牌');
      return;
    }

    try {
      this.setData({ loading: true });

      // 调用设置默认车牌API，使用车牌的id字段
      const res = await apiSetDefaultLicensePlate({
        id: plate.id
      });

      console.log('apiSetDefaultLicensePlate', res);
      if (res && res.code === 0) {
        // 设置成功，刷新车牌列表
        this.showMessage('设置默认车牌成功', 'success');

        // 重新获取车牌列表
        await this.fetchPlateList();
      } else {
        this.setData({ loading: false });
        const errorMsg = res && res.msg ? res.msg : '设置默认车牌失败';
        this.showMessage(errorMsg);
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('设置默认车牌错误:', error);
      this.showMessage('设置默认车牌失败，请重试');
    }
  },

  // 返回上一页
  onBackTap() {
    wx.navigateBack();
  },

  // 格式化时间
  formatTime(timeString) {
    if (!timeString) return '';

    try {
      const date = new Date(timeString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('时间格式化错误:', error);
      return timeString;
    }
  },

  // 显示消息
  showMessage(message, type = 'error') {
    const theme = type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'error';
    Message.info({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      single: true,
      content: message,
      theme: theme
    });
  }
});
