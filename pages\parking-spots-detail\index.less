@import '/variable.less';

/* 主容器样式 */
.parking-detail-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  padding-bottom: 220rpx; /* 减小底部空间，与底部操作栏高度匹配 */
  box-sizing: border-box;
}

.parking-detail-content {
  padding: 0 0 20rpx 0;
  position: relative;
}

/* 导航栏下方的间距 */
gradient-navbar {
  margin-bottom: 20rpx;
}

/* 车位名称容器 */
.spot-name-container {
  padding: 0 30rpx;
  text-align: center;
}

.spot-name-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8rpx;

  .spot-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
    margin-right: 12rpx;
  }

  .status-tag {
    padding: 4rpx 12rpx;
    border-radius: 6rpx;
    font-size: 22rpx;
    text-align: center;
    white-space: nowrap;

    &.status-free {
      color: @bg-color-white;
      background-color: rgba(7, 193, 96, 0.8);
    }

    &.status-busy {
      color: @bg-color-white;
      background-color: rgba(250, 81, 81, 0.8);
    }
  }
}

.spot-location {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}


/* 这个样式已经在 .spot-name-row 中定义，这里移除 */

.spot-info-row {
  display: flex;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 0;
  backdrop-filter: blur(10rpx);
  margin: 0 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 16rpx; /* 减小内边距 */
  align-items: center;
  text-align: center;
  justify-content: center;
  height: 100rpx; /* 减小高度 */
}

.info-item-time {
  flex: 3; /* 共享时段占用更多空间 */
}

.info-item-price {
  flex: 2; /* 价格占用较少空间 */
}

.info-label {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36rpx; /* 减小高度 */
  margin-bottom: 12rpx; /* 减小底部间距 */

  .t-icon {
    margin-right: 8rpx; /* 增加右侧间距 */
    // background: rgba(255, 255, 255, 0.2);
    // border-radius: 30%;
    padding: 8rpx;
  }

  .text {
    font-size: 24rpx; /* 减小字体大小 */
    opacity: 0.9;
    font-weight: 400;
    letter-spacing: 2rpx; /* 增加字母间距 */
  }
}

.info-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 35rpx;
  height: 35rpx;
  width: 100%;
  overflow: hidden;
}

.info-divider {
  width: 2rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 10rpx 0;
  align-self: stretch;
  height: 80%;
  position: relative;
  left: -10rpx; /* 稍微向左调整，使分隔线更靠近共享时段 */
}

.value-text {
  font-size: 30rpx; /* 减小字体大小 */
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.cross-day-tag {
  margin-left: 8rpx;
  font-size: 22rpx;
  color: #fff;
  background-color: rgba(250, 157, 59, 0.8);
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.spot-address {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin: 16rpx 24rpx 0;

  t-icon {
    margin-right: 8rpx;
  }

  text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
  }
}

/* 这些样式已经在 .spot-name-row 中定义，这里移除 */

/* 表单区域 */
.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 40rpx 24rpx 24rpx; /* 增加上边距，与浮动头部保持距离 */
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 24rpx;
  border-bottom: 1rpx solid #F0F0F0;

  &:last-child {
    border-bottom: none;
  }
}

.form-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 400;
  display: flex;
  align-items: center;
  position: relative;
  min-width: 180rpx;
}

.deposit-tip {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background-color: #FA9D3B;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  line-height: 1;
  font-weight: 400;
}

.cap-amount-tip {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background-color: #0052D9;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  line-height: 1;
  font-weight: 400;
}

.form-value {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;

  .placeholder {
    color: #CCCCCC;
  }

  t-icon {
    margin-left: 12rpx;
  }
}

.status-success {
  color: #07C160;
}

.status-warning {
  color: #FA5151;
}

/* 时间选择器 */
.time-selector {
  display: flex;
  align-items: center;
}

.time-control {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-value {
  margin: 0 20rpx;
  min-width: 100rpx;
  text-align: center;
  font-size: 32rpx;
}

.fee-amount {
  font-size: 44rpx;
  color: #FA5151;
  font-weight: 600;
}

/* 温馨提示 */
.notice-section {
  margin: 0 24rpx 24rpx;
  background-color: #FFF7E6;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  position: relative;
  z-index: 1;
}

/* 车位地址信息部分已移除 */

.notice-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #FA9D3B;
  font-weight: 500;
  margin-bottom: 12rpx;

  t-icon {
    margin-right: 8rpx;
  }
}

.notice-content {
  padding: 0;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 车位地址信息相关样式已移除 */

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 16rpx 40rpx 30rpx; /* 减小上下内边距 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05); /* 减轻阴影 */
  z-index: 95; /* 高于内容，低于导航栏 */
  border-top: 1rpx solid #f0f0f0;
}

/* 用户协议 */
.agreement-section {
  display: flex;
  align-items: center;
  // margin-bottom: 24rpx;
  :global(.t-checkbox) {
    padding: 0;
    margin-right: 8rpx;
  }

  :global(.t-checkbox__icon) {
    font-size: 36rpx !important;
  }

  :global(.t-checkbox__label) {
    padding: 0;
  }
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
  padding-top: 2rpx;
}

.agreement-link {
  color: @brand7-normal;
}

.order-button {
  height: 88rpx !important; /* 减小按钮高度 */
  font-size: 32rpx !important; /* 减小字体大小 */
  border-radius: 44rpx !important; /* 调整圆角 */
  font-weight: 500 !important;
  letter-spacing: 2rpx !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.15) !important; /* 减轻阴影 */

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 4rpx rgba(0, 82, 217, 0.15) !important;
  }
}

.deposit-button {
  background-color: #00A870 !important; /* 更优雅的绿色按钮 */
  border-color: #00A870 !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 168, 112, 0.15) !important;
  color: rgba(255, 255, 255, 0.95) !important; /* 稍微柔和的白色文字 */

  &:active {
    background-color: #009A65 !important; /* 按下时稍微深一点的绿色 */
    box-shadow: 0 2rpx 4rpx rgba(0, 168, 112, 0.15) !important;
  }

  &.t-button--disabled {
    background-color: rgba(0, 168, 112, 0.4) !important;
    border-color: rgba(0, 168, 112, 0.4) !important;
    color: rgba(255, 255, 255, 0.7) !important; /* 禁用时更柔和的文字颜色 */
  }
}

/* 弹窗样式 */
.popup-container {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 24rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.popup-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
  padding: 0 24rpx;
}

.popup-footer {
  padding: 24rpx;
  border-top: 1rpx solid #F0F0F0;
}

/* 车牌列表 */
.plate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F0F0F0;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    .plate-text {
      color: @brand7-normal;
      font-weight: 500;
    }
  }
}

.plate-info {
  display: flex;
  align-items: center;
}

.plate-text {
  font-size: 32rpx;
  color: #333;
}

.default-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background-color: @brand7-normal;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  line-height: 1;
  font-weight: 400;
}

.plate-actions {
  display: flex;
  align-items: center;

  t-icon {
    margin-left: 20rpx;
    padding: 10rpx;
  }
}

.empty-plate-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
  }
}

.add-plate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  color: @brand7-normal;
  font-size: 30rpx;

  t-icon {
    margin-right: 8rpx;
  }
}

/* 添加车牌输入 */
.input-container {
  padding: 24rpx 0;
}

.input-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.plate-input {
  width: 100%;
  height: 88rpx;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  margin-bottom: 12rpx;
}

.input-placeholder {
  color: #CCCCCC;
}

.input-tips {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.confirm-btn {
  width: 100%;
}
