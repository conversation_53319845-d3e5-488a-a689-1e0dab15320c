// behaviors/useLoginOverlay.js
module.exports = Behavior({
  data: {
    loginOverlayVisible: false,
    loginErrorMessage: ''
  },

  // 添加属性用于存储待执行的回调函数
  properties: {
    pendingCallback: null
  },

  pageLifetimes: {
    // 页面显示时检查登录状态和登录弹窗标记
    show() {
      this.checkLoginOverlayFlag();
    }
  },

  methods: {
    // 检查是否需要显示登录弹窗（包括检查show_login_overlay标记）
    checkLoginOverlayFlag() {
      const token = wx.getStorageSync('access_token');
      const showLoginOverlay = wx.getStorageSync('show_login_overlay');
      const loginErrorMessage = wx.getStorageSync('login_error_message');

      // console.log('useLoginOverlay 检查登录状态:', {
      //   token: token ? '存在' : '不存在',
      //   showLoginOverlay: showLoginOverlay ? '是' : '否',
      //   loginErrorMessage
      // });
      // 如果有错误消息，保存到data中
      if (loginErrorMessage) {
        this.setData({ loginErrorMessage });
      }
      // 如果没有token或者有显示登录弹窗的标记，则显示登录弹窗
      if (!token || showLoginOverlay) {
        // console.log('需要显示登录弹窗，设置 loginOverlayVisible = true');
        this.setData({
          loginOverlayVisible: true
        });

        // 使用后清除标记
        if (showLoginOverlay) {
          wx.removeStorageSync('show_login_overlay');
        }
      } else {
        // console.log('不需要显示登录弹窗，设置 loginOverlayVisible = false');
        this.setData({
          loginOverlayVisible: false
        });
      }

      return !!token;
    },

    // Show login overlay if user is not logged in
    showLoginOverlayIfNeeded() {
      const token = wx.getStorageSync('access_token');
      this.setData({
        loginOverlayVisible: !token
      });
      return !!token;
    },

    // Handle overlay click
    onLoginOverlayClick(e) {
      this.setData({
        loginOverlayVisible: false
      });
    },

    // Check if action requires login
    checkLoginBeforeAction(callback) {
      const isLoggedIn = this.showLoginOverlayIfNeeded();
      if (isLoggedIn && typeof callback === 'function') {
        // 如果已登录，直接执行回调
        callback();
      } else if (typeof callback === 'function') {
        // 如果未登录，保存回调函数，以便登录成功后执行
        this.pendingCallback = callback;
      }
    },

    // 登录成功后执行待处理的回调
    executePendingCallback() {
      if (typeof this.pendingCallback === 'function') {
        // 执行之前保存的回调函数
        this.pendingCallback();
        // 执行后清除
        this.pendingCallback = null;
      }
    }
  }
});