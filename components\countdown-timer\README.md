# CountdownTimer 倒计时组件

一个功能完整的倒计时组件，支持多种显示格式、超时检测、警告提醒等功能。

## 功能特性

- ✅ 支持多种时间格式解析（ISO格式、普通格式）
- ✅ 自动检测超时状态，显示"已超时"
- ✅ 支持多种显示格式（时:分:秒、文本格式、自定义）
- ✅ 智能警告提醒（可自定义阈值）
- ✅ 完整的生命周期管理
- ✅ 丰富的事件回调
- ✅ 响应式设计
- ✅ 可自定义样式

## 使用方法

### 1. 在页面 JSON 中引入组件

```json
{
  "usingComponents": {
    "countdown-timer": "/components/countdown-timer/index"
  }
}
```

### 2. 在 WXML 中使用

```xml
<!-- 基础用法 -->
<countdown-timer 
  end-time="{{reservationEndTime}}"
  bind:timeout="onCountdownTimeout"
  bind:warning="onCountdownWarning"
  bind:change="onCountdownChange"
/>

<!-- 自定义样式 -->
<countdown-timer 
  end-time="{{reservationEndTime}}"
  format="text"
  timeout-text="时间已到"
  custom-class="my-countdown"
  custom-style="margin: 20rpx 0;"
/>

<!-- 自定义警告阈值 -->
<countdown-timer 
  end-time="{{reservationEndTime}}"
  warning-thresholds="{{[1800, 600, 300]}}"
  bind:warning="onWarning"
/>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| endTime | String | '' | 结束时间字符串，支持 ISO 格式和普通格式 |
| autoStart | Boolean | true | 是否自动开始倒计时 |
| format | String | 'hms' | 显示格式：'hms'(时:分:秒)、'text'(文本)、'custom'(自定义) |
| timeoutText | String | '已超时' | 超时时显示的文本 |
| warningThresholds | Array | [1800, 600, 300] | 警告时间阈值（秒），默认30分钟、10分钟、5分钟 |
| customStyle | String | '' | 自定义样式 |
| customClass | String | '' | 自定义类名 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 倒计时变化时触发 | {hours, minutes, seconds, totalSeconds, formattedTime} |
| timeout | 倒计时结束/超时时触发 | 无 |
| warning | 达到警告阈值时触发 | {remainingSeconds, threshold, message} |
| finish | 倒计时结束时触发（兼容性事件） | 无 |

## 方法说明

| 方法名 | 说明 | 参数 |
|--------|------|------|
| startCountdown | 开始倒计时 | 无 |
| stopCountdown | 停止倒计时 | 无 |
| pauseCountdown | 暂停倒计时 | 无 |
| resumeCountdown | 恢复倒计时 | 无 |
| getStatus | 获取当前状态 | 无 |

## 样式类名

| 类名 | 说明 |
|------|------|
| .countdown-timer | 组件根容器 |
| .countdown-timer.warning | 警告状态样式 |
| .countdown-timer.danger | 危险状态样式 |

## 使用示例

### 基础倒计时

```javascript
Page({
  data: {
    reservationEndTime: '2024-01-20 15:30:00'
  },

  onCountdownChange(e) {
    const { totalSeconds, formattedTime } = e.detail;
    console.log('剩余时间:', formattedTime);
  },

  onCountdownTimeout() {
    wx.showToast({
      title: '时间已到',
      icon: 'none'
    });
  },

  onCountdownWarning(e) {
    const { message } = e.detail;
    wx.showToast({
      title: message,
      icon: 'none'
    });
  }
});
```

### 手动控制倒计时

```javascript
Page({
  onReady() {
    this.countdownTimer = this.selectComponent('#countdown-timer');
  },

  startTimer() {
    this.countdownTimer.startCountdown();
  },

  pauseTimer() {
    this.countdownTimer.pauseCountdown();
  },

  resumeTimer() {
    this.countdownTimer.resumeCountdown();
  },

  getTimerStatus() {
    const status = this.countdownTimer.getStatus();
    console.log('倒计时状态:', status);
  }
});
```

## 注意事项

1. **时间格式**：支持 ISO 格式（2024-01-20T15:30:00）和普通格式（2024-01-20 15:30:00）
2. **自动清理**：组件会在销毁时自动清理定时器，无需手动处理
3. **超时检测**：组件会在初始化时检查是否已超时，如已超时会直接显示超时状态
4. **性能优化**：使用了防抖机制，避免频繁的状态更新

## 更新日志

- v1.0.0: 初始版本，支持基础倒计时功能
- v1.1.0: 新增超时检测和警告提醒功能
- v1.2.0: 新增多种显示格式和自定义样式支持
