<view class="parking-info-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="停车场详情" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />
  
  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{100}}">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="parking-info-main">
      <view class="parking-name">{{parkingInfo.name}}</view>
      <view class="parking-address">
        <t-icon name="location" size="32rpx" color="#ffffff" />
        <text>{{parkingInfo.address}}</text>
      </view>
    </view>
    
    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary">
      <!-- 停车场照片 -->
      <view class="parking-photo">
        <image src="{{parkingInfo.photoUrl}}" mode="aspectFill" class="photo-image"></image>
      </view>
      
      <!-- 开放时间 -->
      <view class="opening-hours">
        <view class="info-label">
          <t-icon name="time" size="32rpx" color="#ffffff" />
          <text>开放时间</text>
        </view>
        <view class="info-value">{{parkingInfo.openingHours}}</view>
      </view>
      
      <!-- 停车费用 -->
      <view class="parking-fee">
        <view class="info-label">
          <t-icon name="money-circle" size="32rpx" color="#ffffff" />
          <text>停车费用</text>
        </view>
        <view class="info-value">{{parkingInfo.fee}}</view>
      </view>
    </view>
  </floating-header>
  
  <!-- 内容区域 -->
  <view class="parking-info-content">
    <!-- 可用车位卡片 -->
    <view class="card">
      <view class="card-title">可用车位</view>
      <view class="spot-list">
        <view class="spot-item" wx:for="{{parkingInfo.availableSpots}}" wx:key="id">
          <view class="spot-info">
            <view class="spot-number">{{item.number}}</view>
            <view class="spot-type">{{item.type}}</view>
          </view>
          <view class="spot-price">¥{{item.price}}/小时</view>
          <t-button theme="primary" size="small" class="book-button" bindtap="onBookSpot" data-id="{{item.id}}">预约</t-button>
        </view>
      </view>
    </view>
    
    <!-- 设施服务卡片 -->
    <view class="card">
      <view class="card-title">设施服务</view>
      <view class="facility-list">
        <view class="facility-item" wx:for="{{parkingInfo.facilities}}" wx:key="index">
          <t-icon name="{{item.icon}}" size="40rpx" color="#0052D9" />
          <text>{{item.name}}</text>
        </view>
      </view>
    </view>
    
    <!-- 用户评价卡片 -->
    <view class="card">
      <view class="card-title">用户评价</view>
      <view class="rating-overview">
        <view class="rating-score">{{parkingInfo.rating}}</view>
        <view class="rating-stars">
          <t-icon name="star-filled" size="32rpx" color="#FFD700" wx:for="{{5}}" wx:key="index" wx:if="{{index < Math.floor(parkingInfo.rating)}}" />
          <t-icon name="star" size="32rpx" color="#FFD700" wx:for="{{5}}" wx:key="index" wx:if="{{index >= Math.floor(parkingInfo.rating)}}" />
        </view>
        <view class="rating-count">{{parkingInfo.ratingCount}}条评价</view>
      </view>
    </view>
  </view>
</view>
