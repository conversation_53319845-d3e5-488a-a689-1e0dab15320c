Component({
  options: {
    styleIsolation: 'shared',
  },
  properties: {
    navType: {
      type: String,
      value: 'title',
    },
    titleText: String,
    background: {
      type: Boolean,
      value: false
    },
    fixed: {
      type: Boolean,
      value: true
    },
    customStyle: {
      type: String,
      value: ''
    }
  },
  data: {
    visible: false,
    sidebar: [
      {
        title: '首页',
        url: 'pages/home-park/index',
        isSidebar: true,
      },
      {
        title: '车场搜索页',
        url: 'pages/search-park/index',
        isSidebar: false,
      },
      {
        title: '车场列表',
        url: 'pages/parking-list/index',
        isSidebar: false,
      },
      {
        title: '车位列表',
        url: 'pages/parking-spots/index',
        isSidebar: false,
      },
      {
        title: '车位详细',
        url: 'pages/parking-detail/index',
        isSidebar: false,
      },
      {
        title: '账单详细',
        url: 'pages/order-detail/index',
        isSidebar: false,
      },
      {
        title: '车位收益',
        url: 'pages/parking-income/index',
        isSidebar: false,
      },
      {
        title: '全部订单',
        url: 'pages/parking-bill/index',
        isSidebar: false,
      },
      {
        title: '账单详情',
        url: 'pages/parking-bill-detail/index',
        isSidebar: false,
      },
      {
        title: '车位管理',
        url: 'pages/parking-management/index',
        isSidebar: false,
      },
      {
        title: '问题反馈',
        url: 'pages/feedback/index',
        isSidebar: false,
      },
    ],
    statusHeight: 0,
  },
  lifetimes: {
    ready() {
      const statusHeight = wx.getWindowInfo().statusBarHeight;
      this.setData({ statusHeight });
    },
  },
  methods: {
    openDrawer() {
      this.setData({
        visible: true,
      });
    },
    itemClick(e) {
      const that = this;
      const { isSidebar, url } = e.detail.item;
      if (isSidebar) {
        wx.switchTab({
          url: `/${url}`,
        }).then(() => {
          // 防止点回tab时，sidebar依旧是展开模式
          that.setData({
            visible: false,
          });
        });
      } else {
        wx.navigateTo({
          url: `/${url}`,
        }).then(() => {
          that.setData({
            visible: false,
          });
        });
      }
    },

    searchTurn() {
      wx.navigateTo({
        url: `/pages/parking-search/index`,
      });
    },
  },
});
