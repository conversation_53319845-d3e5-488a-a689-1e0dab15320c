import Mock from './WxMock';
// 导入包含path和data的对象
import loginMock from './login/index';
import homeMock from './home/<USER>';
import searchMock from './search/index';
import searchParkMock from './search-park/index';
import dataCenter from './dataCenter/index';
import my from './my/index';

import homeParkMock from './home-park/index';
import parkingDetailMock from './parking-detail/index';
import orderDetailMock from './order-detail/index';
import parkingListMock from './parking-list/index';

export default () => {
  // 在这里添加新的mock数据
  const mockData = [
    ...loginMock, ...homeMock, ...searchMock, ...searchParkMock, ...dataCenter, ...my,
    ...homeParkMock, ...parkingDetailMock, ...orderDetailMock, ...parkingListMock
  ];
  mockData.forEach((item) => {
    Mock.mock(item.path, { statusCode: 200, success: true, data: item.data });
  });
};
