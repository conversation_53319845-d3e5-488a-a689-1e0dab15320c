@import '/variable.less';

/* 浮动头部组件 */
.floating-header {
  background: linear-gradient(135deg, #0052D9 0%, #1867FF 100%);
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 90; /* 低于导航栏的z-index */
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 82, 217, 0.12);
  overflow: hidden;
  padding: 160rpx 0 30rpx; /* 顶部留出导航栏的空间 */
  transition: all 0.3s ease-in-out; /* 添加过渡效果 */

  /* 装饰性元素 */
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    z-index: 1;

    &.top-right {
      top: -100rpx;
      right: -100rpx;
      width: 300rpx;
      height: 300rpx;
      background: rgba(255, 255, 255, 0.1);
    }

    &.bottom-left {
      bottom: -80rpx;
      left: -80rpx;
      width: 200rpx;
      height: 200rpx;
      background: rgba(255, 255, 255, 0.08);
    }

    &.bottom-right {
      bottom: 60rpx;
      right: 40rpx;
      width: 120rpx;
      height: 120rpx;
      background: rgba(255, 255, 255, 0.05);
    }
  }

  /* 主要内容区域 */
  .header-content {
    position: relative;
    z-index: 2;
    padding: 0 30rpx;
  }

  /* 次要内容区域 */
  .secondary-content {
    margin-top: 20rpx;
    transition: all 0.3s ease-in-out;
    will-change: opacity, height; /* 优化动画性能 */
    transform-origin: top center; /* 设置变换原点 */
  }

  /* 折叠状态 */
  &.collapsed {
    // padding-top: 100rpx; /* 增加顶部内边距，与导航栏保持距离 */
    max-height: 180rpx; /* 限制最大高度 */
    overflow: hidden;
    border-radius: 0 0 16rpx 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.1);

    /* 隐藏次要内容 */
    .secondary-content {
      display: none !important; /* 强制隐藏 */
      height: 0 !important; /* 确保高度为0 */
      overflow: hidden !important; /* 确保内容不可见 */
      margin: 0 !important; /* 移除外边距 */
      padding: 0 !important; /* 移除内边距 */
      opacity: 0; /* 设置透明度为0 */
      pointer-events: none; /* 禁用鼠标事件 */
    }

    /* 减小装饰圆的大小 */
    .decoration-circle {
      opacity: 0.3;
      transform: scale(0.6);
    }

    /* 添加提示箭头 */
    &::after {
      content: '';
      position: absolute;
      bottom: 16rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 2rpx;
    }
  }
}

/* 占位元素 */
.header-placeholder {
  width: 100%;
}
