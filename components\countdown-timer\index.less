@import '../../variable.less';

.countdown-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 超时状态样式 */
.countdown-timeout {
  .timeout-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #FA9D3B;
    background: rgba(250, 157, 59, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    border: 1px solid rgba(250, 157, 59, 0.3);
  }
}

/* 激活状态样式 */
.countdown-active {
  /* HMS格式样式 */
  .countdown-hms {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .time-unit {
      font-size: 48rpx;
      font-weight: 600;
      color: @gray3;
      background: rgba(0, 82, 217, 0.1);
      padding: 12rpx 16rpx;
      border-radius: 8rpx;
      min-width: 80rpx;
      text-align: center;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }
    
    .time-separator {
      font-size: 48rpx;
      font-weight: 600;
      color: @gray3;
      margin: 0 8rpx;
      opacity: 0.8;
    }
  }
  
  /* 文本格式样式 */
  .countdown-text {
    .time-text {
      font-size: 32rpx;
      font-weight: 500;
      color: @gray3;
      background: rgba(0, 82, 217, 0.1);
      padding: 12rpx 20rpx;
      border-radius: 12rpx;
      border: 1px solid rgba(0, 82, 217, 0.2);
    }
  }
}

/* 未激活状态样式 */
.countdown-inactive {
  .inactive-text {
    font-size: 48rpx;
    font-weight: 600;
    color: #CCCCCC;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .countdown-active {
    .countdown-hms {
      .time-unit {
        font-size: 40rpx;
        padding: 10rpx 14rpx;
        min-width: 70rpx;
      }
      
      .time-separator {
        font-size: 40rpx;
        margin: 0 6rpx;
      }
    }
    
    .countdown-text {
      .time-text {
        font-size: 28rpx;
        padding: 10rpx 16rpx;
      }
    }
  }
  
  .countdown-timeout {
    .timeout-text {
      font-size: 28rpx;
      padding: 6rpx 12rpx;
    }
  }
  
  .countdown-inactive {
    .inactive-text {
      font-size: 40rpx;
    }
  }
}

/* 动画效果 */
.countdown-active .countdown-hms .time-unit {
  transition: all 0.3s ease;
}

.countdown-active .countdown-hms .time-unit:hover {
  transform: scale(1.05);
}

/* 警告状态样式（可通过外部类名控制） */
.countdown-timer.warning {
  .countdown-active .countdown-hms .time-unit {
    color: #FA9D3B;
    background: rgba(250, 157, 59, 0.1);
    border: 1px solid rgba(250, 157, 59, 0.3);
  }
  
  .countdown-active .countdown-hms .time-separator {
    color: #FA9D3B;
  }
  
  .countdown-active .countdown-text .time-text {
    color: #FA9D3B;
    background: rgba(250, 157, 59, 0.1);
    border-color: rgba(250, 157, 59, 0.3);
  }
}

/* 危险状态样式（可通过外部类名控制） */
.countdown-timer.danger {
  .countdown-active .countdown-hms .time-unit {
    color: #E34D59;
    background: rgba(227, 77, 89, 0.1);
    border: 1px solid rgba(227, 77, 89, 0.3);
  }
  
  .countdown-active .countdown-hms .time-separator {
    color: #E34D59;
  }
  
  .countdown-active .countdown-text .time-text {
    color: #E34D59;
    background: rgba(227, 77, 89, 0.1);
    border-color: rgba(227, 77, 89, 0.3);
  }
}
