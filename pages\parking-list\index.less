@import '/variable.less';

.parking-list-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  position: relative;

  .parking-list-content {
    padding: 24rpx 24rpx 120rpx;
    position: relative;
    z-index: 1;
  }

  /* 浮动头部样式 */
  .header-main {
    padding: 20rpx 0;

    .header-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
    }
  }

  .header-secondary {
    padding: 20rpx 0;
  }
}

/* 停车场列表样式 */
.parking-list {
  margin-top: 20rpx;
}

/* 停车场卡片样式 */
.parking-item {
  margin-bottom: 30rpx;
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }
}

.parking-card {
  display: flex;
  background-color: @bg-color-white;
  border-radius: 30rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 停车场图片样式 */
.parking-image-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.parking-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.parking-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 停车场信息样式 */
.parking-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 停车场头部信息 */
.parking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.parking-name {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
  line-height: 1.2;
  flex: 1;
}

.parking-type {
  font-size: 22rpx;
  color: @brand7-normal;
  background-color: rgba(0, 82, 217, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  margin-left: auto;
  flex-shrink: 0;
}

.parking-address {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #959595;
  margin-bottom: 8rpx;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.parking-distance {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #959595;
  margin-bottom: 16rpx;

  .distance-info {
    display: flex;
    align-items: center;

    t-icon {
      margin-right: 8rpx;
    }
  }
}

/* 分隔线样式 */
.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 12rpx 0;
}

/* 底部元数据样式 */
.parking-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

/* 余位信息样式 */
.parking-spots {
  display: flex;
  align-items: center;
  font-size: 26rpx;

  .spots-label {
    color: @gy1;
    margin-right: 8rpx;
  }

  .spots-value {
    color: @brand7-normal;
    font-weight: 500;

    &:empty::after {
      content: '未知';
      color: #999999;
    }
  }
}

/* 价格信息样式 */
.parking-price {
  display: flex;
  align-items: baseline;

  .price-value {
    font-size: 30rpx;
    color: @gy1;
    font-weight: 500;

    &:empty::after {
      content: '价格未知';
      color: #999999;
      font-weight: 400;
      font-size: 26rpx;
    }
  }

  .price-unit {
    font-size: 24rpx;
    color: #959595;
    margin-left: 2rpx;
  }
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: @bg-color-white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #999999;
}

/* 下拉刷新样式优化 */
.t-pull-down-refresh {
  &__track {
    background-color: transparent;
  }

  &__text {
    color: #999999;
    font-size: 26rpx;
  }
}

/* 加载更多样式 */
.load-more-wrapper {
  padding: 24rpx 0 40rpx;
  text-align: center;

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;

    .loading-text {
      margin-left: 12rpx;
      color: #999999;
      font-size: 28rpx;
    }
  }

  .load-more-btn {
    display: inline-block;
    padding: 16rpx 32rpx;
    color: @brand7-normal;
    font-size: 28rpx;
    background-color: rgba(0, 82, 217, 0.05);
    border-radius: 32rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .no-more {
    padding: 20rpx 0;
    color: #999999;
    font-size: 28rpx;
  }
}
