import request from './request';
export default {
  apiNearlyPark: async (payload) => {
    const res = await wx.getLocation({ type: 'wgs84' })
    const { latitude, longitude, speed, accuracy } = res
    const listRes = await request('/mini/park/nearby', 'POST', {
      model: {
        latitude: latitude,
        longitude: longitude,
        radius: 3000
      },
      current: 0,
      size: 10,
      sort: 'id',
      order: 'descending'
    })
    return listRes.data
  },
  /* apiAllPark 响应数据结构示例
    1. 查询到有效停车场列表的响应内容:
    {
        "code": 0,
        "data": {
            "curPage": "0",
            "pages": "3",
            "results": [
                {
                    "id": "1920717154796822529",
                    "parkNo": "1887991720460825",
                    "parkName": "拱墅区测试停车场",
                    "longitude": "30.25961",
                    "latitude": "120.13026",
                    "parkAddress": "杭州市拱墅区测试街道测试停车场",
                    "parkPhone": null,
                    "parkType": {
                        "code": "OPEN_AIR_PARKING",
                        "desc": "开放式车场"
                    },
                    "availableSlots": 5,
                    "distance": "1125932.2849611954",
                    "unitCharge": "10.00",
                    "timeUnit": {
                        "code": "HOUR",
                        "desc": "小时"
                    }
                }
            ],
            "size": "10",
            "total": "25",
            "extraData": null,
            "cursor": null
        },
        "msg": "成功",
        "path": null,
        "timestamp": "1747119807277",
        "errorMsg": null
    }

    2. 最后一页无数据的响应内容:
    {
        "code": 0,
        "data": {
            "curPage": "3",
            "pages": "3",
            "results": [],
            "size": "10",
            "total": "25",
            "extraData": null,
            "cursor": null
        },
        "msg": "成功",
        "path": null,
        "timestamp": "1747311734632",
        "errorMsg": null
    }
  */
  apiAllPark: async (payload) => {
    const { current } = payload
    const res = await wx.getLocation({ type: 'wgs84' })
    const { latitude, longitude, speed, accuracy } = res
    const listRes = await request('/mini/park/list', 'POST', {
      model: {
        latitude: latitude,
        longitude: longitude,
        parkName: ""
      },
      current,
      size: 5,
      sort: 'id',
      order: 'descending'
    })
    return listRes.data
  },
  /*
  apiGenOrder响应数据示例
    {
        "code": 0,
        "data": {
            "timeStamp": "1746952961",
            "signType": "HMAC-SHA256",
            "package": "prepay_id=wx11164235127501886f0159db4827d10001",
            "paySign": "46B49CF8AA876A793494EC94DE95338E4D255D33BE419207C665DCC6ECD76F25",
            "nonceStr": "1746952961792",
            "appId": "wxa5f7c5d4b5c1d034",
            "id":1,
        },
        "msg": "成功",
        "path": null,
        "timestamp": "1746952963840",
        "errorMsg": null
    }
  */
  apiGenOrder: async (payload) => {
    const { payType } = payload
    const uniId = await request('/mini/park/deposit/unique/id/generate', 'POST')
    const listRes = await request('/mini/park/deposit/pay', 'POST', {
      "uniqueId": uniId.data.data,
      "payType": payType,
      "payMethod": "WXPAY_LITE",
      "payAmount": 99
    })
    return listRes.data
  },

  /* apiQueryPark 响应数据结构示例

    无车位的时候的响应:{
        "code": 0,
        "data": null,
        "msg": "成功",
        "path": null,
        "timestamp": "1747295204393",
        "errorMsg": null
    }
    ,
    有车位时的响应:{
        "code": 0,
        "data": {
            "id": "1922843719026405378",
            "parkNo": "1887854886437049",
            "parkName": "杭州市上城区停车场",
            "longitude": "30.29869",
            "latitude": "120.15318",
            "parkAddress": "浙江省杭州市上城区停车场",
            "parkType": {
                "code": "ENCLOSED_GARAGE",
                "desc": "封闭式车场"
            },
            "availableSlots": 1,
            "distance": "940947.5528667468",
            "unitCharge": "15.00",
            "averagePrice": "10.00",
            "timeUnit": {
                "code": "HOUSR",
                "desc": "小时"
            }
        },
        "msg": "成功",
        "path": null,
        "timestamp": "1747301317387",
        "errorMsg": null
    }
  */
  apiQueryPark: async (payload) => {
    const { id, longitude, latitude } = payload
    const queryRes = await request('/mini/park/query', 'POST', {
      id,
      longitude,
      latitude
    })
    return queryRes.data
  },

  /*
  apiFeedbackCreate 接口说明
  请求参数:
  {
    "type": "DEDUCTION_ISSUE",      // 类型: DEDUCTION_ISSUE-扣款异常, LOCK_OPERATION_ISSUE-开关锁异常, DEPOSIT_NOT_REFUNDED-保证金未退还, PAGE_LOADING_FAILURE-页面加载失败, OTHER-其他
    "remark": "点击关锁，没有变化",   // 备注
    "phone": "18575647898"          // 电话号
  }

  响应数据结构示例:
    {
      "code": 0,
      "data": "1923252422300999682",
      "msg": "成功",
      "path": null,
      "timestamp": "1747374084460",
      "errorMsg": null
    }
  */
  apiFeedbackCreate: async (payload) => {
    const { type, remark, phone } = payload
    const feedbackRes = await request('/mobile/feedback/create', 'POST', {
      type,
      remark,
      phone
    })
    return feedbackRes.data
  },

  /* apiGetParkLockList 响应数据结构示例
    1. 有数据的响应内容:
    {
      "code": 0,
      "data": {
          "curPage": "0",
          "pages": "1",
          "results": [
              {
                  "ruleId": "1922845176542851074",
                  "lockId": "1922845176509296642",
                  "lotId": "1922845176475742210",
                  "code": "B101",
                  "location": "地下一层",
                  "shareStartTime": "10:43:00",
                  "shareEndTime": "10:43:00",
                  "price": "10.00",
                  "isCrossDay": {
                      "code": "NO",
                      "desc": "否"
                  },
                  "useStatus": {
                      "code": "NO",
                      "desc": "否"
                  },
                  "ifAvailable": {
                      "code": "NO",
                      "desc": "否"
                  }
              },
              {
                  "ruleId": "1922845373498978305",
                  "lockId": "1922845373465423874",
                  "lotId": "1922845373431869442",
                  "code": "B102",
                  "location": "地下一层",
                  "shareStartTime": "15:16:39",
                  "shareEndTime": "15:16:43",
                  "price": "10.00",
                  "isCrossDay": {
                      "code": "NO",
                      "desc": "否"
                  },
                  "useStatus": {
                      "code": "NO",
                      "desc": "否"
                  },
                  "ifAvailable": {
                      "code": "NO",
                      "desc": "否"
                  }
              }
          ],
          "size": "3",
          "total": "2",
          "extraData": null,
          "cursor": null
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747380644268",
      "errorMsg": null
    }

    2. 无数据的响应内容:
    {
      "code": 0,
      "data": {
          "curPage": "1",
          "pages": "0",
          "results": [],
          "size": "3",
          "total": "0",
          "extraData": null,
          "cursor": null
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747380524127",
      "errorMsg": null
    }
  */
  apiGetParkLockList: async (payload) => {
    const { current = 1, size = 5, parkId } = payload
    const lockListRes = await request('/device/lock/getParkLockList', 'POST', {
      current,
      size,
      model: {
        parkId
      }
    })
    return lockListRes.data
  },

  /* apiGetRuleByLockId 响应数据结构示例
    {
      "code": 0,
      "data": {
          "id": "1922845176542851074",
          "parkId": "1922843368063823874",
          "lotId": "1922845176475742210",
          "lockId": "1922845176509296642",
          "price": "10.00",
          "shareStartTime": "01:43:00",
          "shareEndTime": "23:43:00",
          "isCrossDay": {
              "code": "NO",
              "desc": "否"
          },
          "shareStatus": null,
          "capAmount": "30.00",
          "location": "地下一层",
          "code": "B101",
          "parkName": null,
          "parkAddress": "浙江省杭州市拱墅区停车场",
          "useStatus": {
              "code": "NO",
              "desc": "否"
          }
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747387646803",
      "errorMsg": null
    }
  */
  apiGetRuleByLockId: async (payload) => {
    const { lockId } = payload
    const ruleRes = await request(`/device/lock/getRuleByLockId/${lockId}`, 'POST')
    return ruleRes.data
  },

  /* apiGetDepositAmount 响应数据结构示例
    1. 已支付押金的响应内容:
    {
      "code": 0,
      "data": "99.00",
      "msg": "成功",
      "path": null,
      "timestamp": "*************",
      "errorMsg": null
    }

    2. 未支付押金的响应内容:
    {
      "code": 0,
      "data": "0.00",
      "msg": "成功",
      "path": null,
      "timestamp": "*************",
      "errorMsg": null
    }
  */
  apiGetDepositAmount: async () => {
    const depositRes = await request('/mini/account/getDepositAmount', 'POST')
    return depositRes.data
  },

  /* apiCreateOrder 响应数据结构示例
    1. 成功的响应内容:
    {
      "code": 0,
      "data": "1923565208410341378",
      "msg": "成功",
      "path": null,
      "timestamp": "*************",
      "errorMsg": null
    }

    2. 失败的响应内容:
    {
      "code": -9,
      "data": null,
      "msg": "下单押金金额不能为0",
      "path": "/mini/park/order/create",
      "timestamp": "*************",
      "errorMsg": "下单押金金额不能为0"
    }
  */
  apiCreateOrder: async (payload) => {
    const { source = 'MINI_APP', amount = 0, lockId, plateNo, reservationDuration } = payload
    const orderRes = await request('/mini/park/order/create', 'POST', {
      source,
      amount,
      lockId,
      plateNo,
      reservationDuration
    })
    return orderRes.data
  },

  /* apiCancelOrder 响应数据结构示例
    1. 成功的响应内容:
    {
      "code": 0,
      "data": "1922475333648973826",
      "msg": "成功",
      "path": null,
      "timestamp": "1747188930128",
      "errorMsg": null
    }

    2. 失败的响应内容:
    {
      "code": -10,
      "data": null,
      "msg": "当前订单数据不存在",
      "path": "/mini/park/order/cancel",
      "timestamp": "1747622909743",
      "errorMsg": "当前订单数据不存在"
    }
  */
  apiCancelOrder: async (payload) => {
    const { id } = payload
    const cancelRes = await request('/mini/park/order/cancel', 'POST', {
      id
    })
    return cancelRes.data
  },

  /* apiCreateLicensePlate 响应数据结构示例
    {
      "code": 0,
      "data": "1924187970378633217",
      "msg": "成功",
      "path": null,
      "timestamp": "1747597136624",
      "errorMsg": null
    }
  */
  apiCreateLicensePlate: async (payload) => {
    const { plateNo, isDefault = 1 } = payload
    const plateRes = await request('/mini/park/licensePlate/create', 'POST', {
      plateNo,
      isDefault
    })
    return plateRes.data
  },

  /* apiDeleteLicensePlate 响应数据结构示例
    {
      "code": 0,
      "data": "",
      "msg": "成功",
      "path": null,
      "timestamp": "1747597136624",
      "errorMsg": null
    }
  */
  apiDeleteLicensePlate: async (payload) => {
    const { data } = payload
    const deleteRes = await request('/mini/park/licensePlate/delete', 'POST', {
      data
    })
    return deleteRes.data
  },

  /* apiGetLicensePlateList 响应数据结构示例
    {
      "code": 0,
      "data": [
          {
              "id": "1924185283041869826",
              "createdBy": "1922104193555427329",
              "createdTime": "2025-05-19 03:28:16",
              "updatedBy": "1922104193555427329",
              "updatedTime": "2025-05-19 03:28:16",
              "plateNo": "A34567",
              "plateColor": "BLUE",
              "isDefault": {
                  "code": "NO",
                  "desc": "否"
              }
          },
          {
              "id": "1924187865848188930",
              "createdBy": "1922104193555427329",
              "createdTime": "2025-05-19 03:38:32",
              "updatedBy": "1922104193555427329",
              "updatedTime": "2025-05-19 03:38:32",
              "plateNo": "A34568",
              "plateColor": "BLUE",
              "isDefault": {
                  "code": "YES",
                  "desc": "是"
              }
          },
          {
              "id": "1924187970378633217",
              "createdBy": "1922104193555427329",
              "createdTime": "2025-05-19 03:38:56",
              "updatedBy": "1922104193555427329",
              "updatedTime": "2025-05-19 03:38:56",
              "plateNo": "A54568",
              "plateColor": "BLUE",
              "isDefault": {
                  "code": "NO",
                  "desc": "否"
              }
          }
      ],
      "msg": "成功",
      "path": null,
      "timestamp": "1747598463931",
      "errorMsg": null
    }
  */
  apiGetLicensePlateList: async () => {
    const listRes = await request('/mini/park/licensePlate/delete/getList', 'POST')
    return listRes.data
  },

  /* apiGetEarningsList 响应数据结构示例
    1. 有收益数据的响应内容:
    {
      "code": 0,
      "data": {
          "curPage": "0",
          "pages": "1",
          "results": [
              {
                  "id": "2",
                  "parkId": "1922976904607494145",
                  "lotId": "1922845474623647745",
                  "lockId": "3002",
                  "orderId": "2",
                  "parkName": "测试停车场1",
                  "location": "地下一层",
                  "code": "B101",
                  "actualRevenue": "211.50",
                  "settleTime": "2025-05-18 23:59:59"
              },
              {
                  "id": "3",
                  "parkId": "1922843368063823874",
                  "lotId": "1922845474623647745",
                  "lockId": "3003",
                  "orderId": "3",
                  "parkName": "杭州市拱墅区测试停车场",
                  "location": "地下一层",
                  "code": "B101",
                  "actualRevenue": "0.00",
                  "settleTime": "2025-05-18 14:45:00"
              },
              {
                  "id": "4",
                  "parkId": "1004",
                  "lotId": "2004",
                  "lockId": "3004",
                  "orderId": "4",
                  "parkName": null,
                  "location": null,
                  "code": null,
                  "actualRevenue": "0.00",
                  "settleTime": "2025-05-18 12:00:00"
              },
              {
                  "id": "1",
                  "parkId": "1922843368063823874",
                  "lotId": "1922845176475742210",
                  "lockId": "3001",
                  "orderId": "1",
                  "parkName": "杭州市拱墅区测试停车场",
                  "location": "地下一层",
                  "code": "B101",
                  "actualRevenue": "27.00",
                  "settleTime": "2025-05-18 10:45:00"
              }
          ],
          "size": "10",
          "total": "4",
          "extraData": null,
          "cursor": null
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747585800179",
      "errorMsg": null
    }

    2. 无收益数据的响应内容:
    {
      "code": 0,
      "data": {
          "curPage": "1",
          "pages": "0",
          "results": [],
          "size": "3",
          "total": "0",
          "extraData": null,
          "cursor": null
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747380524127",
      "errorMsg": null
    }
  */
  apiGetEarningsList: async (payload) => {
    const { current = 1, size = 10, model = {} } = payload
    const earningsRes = await request('/mini/park/earn/pageByCustomer', 'POST', {
      current,
      size,
      model
    })
    return earningsRes.data
  },

  /* apiGetTotalRevenue 响应数据结构示例
    {
      "code": 0,
      "data": "238.50",
      "msg": "成功",
      "path": null,
      "timestamp": "1747586992954",
      "errorMsg": null
    }
  */
  apiGetTotalRevenue: async () => {
    const revenueRes = await request('/mini/park/earn/totalRevenue', 'POST')
    return revenueRes.data
  },

  /* apiGetEarnDetailList 响应数据结构示例
    {
      "code": 0,
      "data": [
          {
              "id": null,                                  // 收入id
              "parkId": "1922843368063823874",            // 车场id
              "lotId": "1922845176475742210",             // 车位id
              "lockId": "3001",                           // 车锁id
              "parkName": "杭州市拱墅区测试停车场",        // 车场名称
              "location": "地下一层",                      // 车位区域
              "code": "B101",                             // 车位编号
              "totalRevenue": "27.00"                     // 总收入
          },
          {
              "id": null,                                  // 收入id
              "parkId": "1922976904607494145",            // 车场id
              "lotId": "1922845474623647745",             // 车位id
              "lockId": "3002",                           // 车锁id
              "parkName": "测试停车场1",                   // 车场名称
              "location": "地下一层",                      // 车位区域
              "code": "B101",                             // 车位编号
              "totalRevenue": "211.50"                    // 总收入
          },
          {
              "id": null,                                  // 收入id
              "parkId": "1922843368063823874",            // 车场id
              "lotId": "1922845474623647745",             // 车位id
              "lockId": "3003",                           // 车锁id
              "parkName": "杭州市拱墅区测试停车场",        // 车场名称
              "location": "地下一层",                      // 车位区域
              "code": "B101",                             // 车位编号
              "totalRevenue": "0.00"                      // 总收入
          },
          {
              "id": null,                                  // 收入id
              "parkId": "1004",                           // 车场id
              "lotId": "2004",                            // 车位id
              "lockId": "3004",                           // 车锁id
              "parkName": null,                           // 车场名称
              "location": null,                           // 车位区域
              "code": null,                               // 车位编号
              "totalRevenue": "0.00"                      // 总收入
          }
      ],
      "msg": "成功",
      "path": null,
      "timestamp": "1747588174367",
      "errorMsg": null
    }
  */
  apiGetEarnDetailList: async () => {
    const earnDetailRes = await request('/mini/park/earn/getEarnList', 'POST')
    return earnDetailRes.data
  },

  /* apiGetLockTotalRevenue 响应数据结构示例
    {
      "code": 0,
      "data": "238.50",                                  // 金额
      "msg": "成功",
      "path": null,
      "timestamp": "1747589061900",
      "errorMsg": null
    }
  */
  apiGetLockTotalRevenue: async (payload) => {
    const { lockId } = payload
    const revenueRes = await request('/mini/park/earn/totalLockRevenue', 'POST', {
      lockId
    })
    return revenueRes.data
  },

  /* apiGetLockEarningsList 响应数据结构示例
    {
      "code": 0,
      "data": {
          "curPage": "0",                                // 当前页码
          "pages": "1",                                  // 总页数
          "results": [                                   // 收益记录列表
              {
                  "id": "2",                             // 收益记录ID
                  "parkId": "1922976904607494145",       // 车场ID
                  "lotId": "1922845474623647745",        // 车位ID
                  "lockId": "1922845176509296642",       // 车锁ID
                  "orderId": "2",                        // 订单ID
                  "parkName": "测试停车场1",              // 车场名称
                  "location": "地下一层",                 // 车位区域
                  "code": "B101",                        // 车位编号
                  "actualRevenue": "211.50",             // 实际收益
                  "settleTime": "2025-05-18 23:59:59"    // 结算时间
              },
              {
                  "id": "1",                             // 收益记录ID
                  "parkId": "1922843368063823874",       // 车场ID
                  "lotId": "1922845176475742210",        // 车位ID
                  "lockId": "1922845176509296642",       // 车锁ID
                  "orderId": "1",                        // 订单ID
                  "parkName": "杭州市拱墅区测试停车场",    // 车场名称
                  "location": "地下一层",                 // 车位区域
                  "code": "B101",                        // 车位编号
                  "actualRevenue": "27.00",              // 实际收益
                  "settleTime": "2025-05-18 10:45:00"    // 结算时间
              }
          ],
          "size": "10",                                  // 每页记录数
          "total": "2",                                  // 总记录数
          "extraData": null,                             // 额外数据
          "cursor": null                                 // 游标
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747589740296",
      "errorMsg": null
    }
  */
  apiGetLockEarningsList: async (payload) => {
    const { lockId, current = 1, size = 10 } = payload
    const earningsRes = await request('/mini/park/earn/pageByLock', 'POST', {
      current,
      size,
      model: {
        lockId
      }
    })
    return earningsRes.data
  },

  /* apiGetWalletBalance 响应数据结构示例
    {
      "code": 0,
      "data": "199.00",                                 // 钱包余额
      "msg": "成功",
      "path": null,
      "timestamp": "*************",
      "errorMsg": null
    }
  */
  apiGetWalletBalance: async () => {
    const balanceRes = await request('/mini/account/getTotalAmount', 'POST')
    return balanceRes.data
  },

  /* apiGetUsingOrders 响应数据结构示例
    {
      "code": 0,
      "data": [
          {
              "id": "1",                                // 订单id
              "lockId": "3001"                          // 车锁id
          },
          {
              "id": "2",                                // 订单id
              "lockId": "3001"                          // 车锁id
          },
          {
              "id": "3",                                // 订单id
              "lockId": "3001"                          // 车锁id
          }
      ],
      "msg": "成功",
      "path": null,
      "timestamp": "*************",
      "errorMsg": null
    }

    注意: 如果返回list长度为0，则表示没有正在使用中的订单；如果长度不为0，则表示有正在使用中的订单
  */
  apiGetUsingOrders: async () => {
    const ordersRes = await request('/mini/park/order/using', 'POST')
    return ordersRes.data
  },

  /* apiGetOrderDetail 响应数据结构示例
    {
      "code": 0,
      "data": {
          "id": "1",                                    // 订单id
          "source": {                                   // 下单方式
              "code": "SCAN",
              "desc": "扫码下单"
          },
          "parkId": "1001",                             // 车场id
          "parkType": {                                 // 停车场类型
              "code": "ENCLOSED_GARAGE",
              "desc": "封闭式车场"
          },
          "lotId": "2001",                              // 车位id
          "lockId": "3001",                             // 车锁id
          "lockNo": "LOCK-20250518-001",                // 车锁编号
          "orderNo": "ORD-20250518-001",                // 订单编号
          "orderType": {                                // 订单类型
              "code": "ODRINARY",
              "desc": "普通计费"
          },
          "lotShareRuleId": "4001",                     // 共享方案id
          "customerId": "1922104193555427329",          // 小程序用户id
          "parkRecordId": "6001",                       // 停车记录表id
          "plateNo": "粤A12345",                        // 车牌号
          "arriveDatetime": "2025-05-18 08:30:00",      // 抵达时间
          "leaveDatetime": "2025-05-18 10:30:00",       // 出场时间
          "duration": "7200",                           // 停车时长
          "depositAmount": "20.00",                     // 押金
          "depositPayOrderNo": "DEP-20250518-001",      // 保证金充值的订单号
          "reservationStartTime": "2025-05-18 08:00:00", // 预计停车开始时间
          "reservationEndTime": "2025-05-18 10:00:00",  // 预约结束时间
          "reservationDuration": "7200",                // 预计停车时长
          "reservationAmount": "30.00",                 // 预计停车金额
          "orderTime": "2025-05-18 08:15:00",           // 下单时间
          "lockOpenTime": "2025-05-18 08:25:00",        // 开锁时间
          "lastLockOpenTime": "2025-05-18 08:25:00",    // 最后一次开锁时间
          "lockCloseTime": null,                        // 关锁时间
          "freeInTime": 15,                             // 进场免费时间
          "freeOutTime": 15,                            // 出场免费时间
          "amount": "30.00",                            // 订单金额
          "parkAmount": "30.00",                        // 停车费用:正常计算的费用
          "timeoutAmount": "0.00",                      // 违规超时费用
          "payableAmount": "25.00",                     // 应缴金额
          "realPayAmount": "20.00",                     // 订单实付金额
          "unpaidAmount": "5.00",                       // 欠费金额(补缴金额)
          "preferentialAmount": "5.00",                 // 优惠总金额
          "pointDeductAmount": "2.00",                  // 积分抵扣总金额
          "discountAmount": "0.00",                     // 抵扣金额
          "redEnvelopeAmount": "0.00",                  // 红包抵扣金额
          "abnormalAmount": "0.00",                     // 异常优惠金额
          "refundPendingAmount": "0.00",                // 退款金额
          "manualRefundAmount": "0.00",                 // 人工退费金额
          "refundedRealPayAmount": "0.00",              // 已退款金额累计
          "serviceFee": "1.00",                         // 服务费
          "status": {                                   // 订单状态
              "code": "PROCESSING",
              "desc": "进行中"
          },
          "canceledTime": null,                         // 订单取消时间
          "depositRefundStatus": {                      // 押金退还状态
              "code": "REFUNDED",
              "desc": "已退还"
          },
          "paySuccessDatetime": "2025-05-18 10:45:00",  // 支付成功时间
          "supplementTime": null,                       // 补缴时间
          "autoRefundTime": null,                       // 自动退费时间
          "manualRefundTime": null,                     // 人工退费时间
          "settlementCount": 1,                         // 结算次数
          "price": null,                                // 车位单价
          "remark": "正常停车"                           // 备注
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747575738099",
      "errorMsg": null
    }
  */
  apiGetOrderDetail: async (payload) => {
    const { orderId } = payload
    const orderDetailRes = await request(`/mini/park/order/orderDetail/${orderId}`, 'POST')
    return orderDetailRes.data
  },

  /* apiGetOrderList 响应数据结构示例
    {
      "code": 0,
      "data": {
          "curPage": "0",                               // 当前页码
          "pages": "1",                                 // 总页数
          "results": [                                  // 订单列表
              {
                  "id": "1",                            // 订单id
                  "parkId": "1922843719026405378",      // 停车场id
                  "lotId": "1922845474623647745",       // 车位id
                  "lockId": "3001",                     // 车锁id
                  "parkAddress": "浙江省杭州市上城区停车场", // 停车场地址
                  "parkName": "杭州市上城区停车场",       // 停车场名称
                  "location": "地下一层",                // 车位区域
                  "code": "B101",                       // 区域编码
                  "plateNo": "粤A12345",                // 车牌号
                  "duration": "7200",                   // 停车时长
                  "orderTime": "2025-05-18 08:15:00",   // 下单时间
                  "status": {                           // 订单状态
                      "code": "PROCESSING",
                      "desc": "进行中"
                  },
                  "paySuccessDatetime": "2025-05-18 10:45:00", // 付款时间
                  "payableAmount": "25.00",             // 应缴金额
                  "parkAmount": "30.00"                 // 停车费用:正常计算的费用
              },
              // 更多订单...
          ],
          "size": "10",                                 // 每页记录数
          "total": "5",                                 // 总记录数
          "extraData": null,                            // 额外数据
          "cursor": null                                // 游标
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747590482480",
      "errorMsg": null
    }
  */
  apiGetOrderList: async () => {
    const orderListRes = await request('/mini/park/order/pageByCustomerId', 'POST')
    return orderListRes.data
  },

  /* apiOperateLock 响应数据结构示例
    {
      "code": 0,
      "data": null,
      "msg": "成功",
      "path": null,
      "timestamp": "1747594273554",
      "errorMsg": null
    }
  */
  apiOperateLock: async (payload) => {
    const { parkId, lockNumber, status, transmissionType = 0 } = payload
    const operateRes = await request('/device/lock/operateLock', 'POST', {
      parkId,
      lockNumber,
      status,
      transmissionType
    })
    return operateRes.data
  },

  /* apiGetParkLockList 响应数据结构示例
    {
      "code": 0,
      "data": {
          "curPage": "0",                                // 当前页码
          "pages": "1",                                  // 总页数
          "results": [                                   // 车锁列表
              {
                  "ruleId": "1922845176542851074",       // 共享方案id
                  "lockId": "1922845176509296642",       // 车锁id
                  "lotId": "1922845176475742210",        // 车位id
                  "code": "B101",                        // 车位编号
                  "location": "地下一层",                 // 车位区域
                  "shareStartTime": "08:00:00",          // 共享开始时间
                  "shareEndTime": "20:00:00",            // 共享结束时间
                  "price": "10.00",                      // 车位价格
                  "isCrossDay": {                        // 是否跨天
                      "code": "NO",
                      "desc": "否"
                  },
                  "useStatus": {                         // 是否有正在进行的订单
                      "code": "NO",
                      "desc": "否"
                  },
                  "ifAvailable": {                       // 是否可用(是否在共享时段内)
                      "code": "NO",
                      "desc": "否"
                  }
              },
              // 更多车锁...
          ],
          "size": "10",                                  // 每页记录数
          "total": "2",                                  // 总记录数
          "extraData": null,                             // 额外数据
          "cursor": null                                 // 游标
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747570344614",
      "errorMsg": null
    }
  */
  apiGetParkLockList: async (payload) => {
    const { parkId, current = 1, size = 10 } = payload
    const lockListRes = await request('/device/lock/getParkLockList', 'POST', {
      current,
      size,
      model: {
        parkId
      }
    })
    return lockListRes.data
  },

  /* apiGetLockRuleDetail 响应数据结构示例
    {
      "code": 0,
      "data": {
          "id": "1922845176542851074",                   // 共享方案id
          "parkId": "1922843368063823874",               // 车场id
          "lotId": "1922845176475742210",                // 车位id
          "lockId": "1922845176509296642",               // 车锁id
          "price": "10.00",                              // 车位价格
          "shareStartTime": "08:00:00",                  // 共享开始时间
          "shareEndTime": "20:00:00",                    // 共享结束时间
          "isCrossDay": {                                // 是否跨天
              "code": "NO",
              "desc": "否"
          },
          "shareStatus": null,                           // 共享状态
          "capAmount": "30.00",                          // 封顶价格
          "location": "地下一层",                         // 车位区域
          "code": "B101",                                // 车位编号
          "parkName": null,                              // 车场名称
          "parkAddress": "浙江省杭州市拱墅区停车场",       // 车场地址
          "useStatus": {                                 // 是否正在被使用
              "code": "NO",
              "desc": "否"
          }
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747571831833",
      "errorMsg": null
    }
  */
  apiGetLockRuleDetail: async (payload) => {
    const { lockId } = payload
    const ruleDetailRes = await request(`/device/lock/getRuleByLockId/${lockId}`, 'POST')
    return ruleDetailRes.data
  },

  /* apiGetShareTimeOptions 响应数据结构示例
    {
      "code": 0,
      "data": [                                          // 可选择的共享时长（小时）
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9,
          10
      ],
      "msg": "成功",
      "path": null,
      "timestamp": "1747573334360",
      "errorMsg": null
    }
  */
  apiGetShareTimeOptions: async (payload) => {
    const { ruleId } = payload
    const timeOptionsRes = await request(`/mini/rule/time/${ruleId}`, 'POST')
    return timeOptionsRes.data
  },

  /* apiGetLockCount 响应数据结构示例
    {
      "code": 0,
      "data": "2",                                       // 车锁数量
      "msg": "成功",
      "path": null,
      "timestamp": "1747591005158",
      "errorMsg": null
    }
  */
  apiGetLockCount: async () => {
    const countRes = await request('/device/lock/countLocks', 'POST')
    return countRes.data
  },

  /* apiGetPersonalLockList 响应数据结构示例
    {
      "code": 0,
      "data": {
          "curPage": "0",                                // 当前页码
          "pages": "1",                                  // 总页数
          "results": [                                   // 车锁列表
              {
                  "id": "1922845373498978305",           // 共享方案id
                  "parkId": "1922843368063823874",       // 车场id
                  "lotId": "1922845373431869442",        // 车位id
                  "lockId": "1922845373465423874",       // 车锁id
                  "price": "10.00",                      // 车位单价
                  "shareStartTime": "01:16:00",          // 共享开始时间
                  "shareEndTime": "23:16:00",            // 共享结束时间
                  "isCrossDay": {                        // 是否跨天
                      "code": "NO",
                      "desc": "否"
                  },
                  "shareStatus": {                       // 共享状态
                      "code": "YES",
                      "desc": "是"
                  },
                  "capAmount": "20.00",                  // 封顶价格
                  "location": "地下一层",                 // 车位区域
                  "code": "B102",                        // 车位编号
                  "parkName": "杭州市拱墅区测试停车场",    // 车场名称
                  "parkAddress": "浙江省杭州市拱墅区停车场", // 车场地址
                  "useStatus": {                         // 是否正在被使用
                      "code": "NO",
                      "desc": "否"
                  }
              },
              // 更多车锁...
          ],
          "size": "10",                                  // 每页记录数
          "total": "2",                                  // 总记录数
          "extraData": null,                             // 额外数据
          "cursor": null                                 // 游标
      },
      "msg": "成功",
      "path": null,
      "timestamp": "1747593623248",
      "errorMsg": null
    }
  */
  apiGetPersonalLockList: async () => {
    const personalLockRes = await request('/device/lock/getRuleByCustomerId', 'POST')
    return personalLockRes.data
  },

  /* apiUpdateShareRule 响应数据结构示例
    {
      "code": 0,
      "data": null,
      "msg": "成功",
      "path": null,
      "timestamp": "1747594273554",
      "errorMsg": null
    }

    注意: 跨天时，共享开始时间要大于共享结束时间
  */
  apiUpdateShareRule: async (payload) => {
    const {
      id,
      price,
      shareStartTime,
      shareEndTime,
      isCrossDay,
      shareStatus,
      cycleType,
      illegalMultiple,
      capAmount
    } = payload
    const updateRes = await request('/mini/park/rule/update', 'POST', {
      id,
      price,
      shareStartTime,
      shareEndTime,
      isCrossDay,
      shareStatus,
      cycleType,
      illegalMultiple,
      capAmount
    })
    return updateRes.data
  },

  /* apiUpdateShareStatus 响应数据结构示例
    {
      "code": 0,
      "data": null,
      "msg": "成功",
      "path": null,
      "timestamp": "1747594273554",
      "errorMsg": null
    }
  */
  apiUpdateShareStatus: async (payload) => {
    const { id, shareStatus } = payload
    const statusRes = await request('/mini/park/rule/status', 'POST', {
      id,
      shareStatus
    })
    return statusRes.data
  },
};