import Message from 'tdesign-miniprogram/message';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    parkingId: '',
    parkingInfo: {
      name: '南北商务港停车场',
      address: '浙江省杭州市拱墅区温州路71号',
      photoUrl: 'https://example.com/parking.jpg', // 实际项目中应该使用真实图片
      openingHours: '全天开放',
      fee: '白天3元/小时，夜间2元/小时',
      rating: 4.5,
      ratingCount: 128,
      availableSpots: [
        { id: '1', number: 'B1-001', type: '标准车位', price: '3' },
        { id: '2', number: 'B1-002', type: '标准车位', price: '3' },
        { id: '3', number: 'B1-003', type: '标准车位', price: '3' },
        { id: '4', number: 'B2-001', type: '大型车位', price: '5' }
      ],
      facilities: [
        { icon: 'camera', name: '监控' },
        { icon: 'lock', name: '安保' },
        { icon: 'wifi', name: '免费WiFi' },
        { icon: 'shop', name: '便利店' },
        { icon: 'restroom', name: '洗手间' },
        { icon: 'charging', name: '充电桩' },
        { icon: 'elevator', name: '电梯' },
        { icon: 'help', name: '服务台' }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    
    if (id) {
      this.setData({ parkingId: id });
      this.fetchParkingInfo();
    }
  },

  /**
   * 获取停车场信息
   */
  fetchParkingInfo() {
    // 模拟API请求
    // 实际项目中应该使用: const res = await request(`/parking/detail?id=${this.data.parkingId}`);
    
    // 使用静态数据
    setTimeout(() => {
      // 数据已在 data 中初始化，此处可以根据实际情况更新
      this.showMessage('停车场信息加载成功', 'success');
    }, 500);
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 预约车位按钮点击事件
   */
  onBookSpot(e) {
    const { id } = e.currentTarget.dataset;
    
    // 跳转到预约页面
    wx.navigateTo({
      url: `/pages/parking-detail/index?id=${id}`
    });
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
})
