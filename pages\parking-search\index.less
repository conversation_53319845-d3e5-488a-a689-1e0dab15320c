@import '/variable.less';

.page {
  background: linear-gradient(180deg, white 5%, #F5F7FA);
  box-sizing: border-box;
  width: 100vw;
  min-height: 100vh;
  padding: 0 30rpx;
  background-color: @bg-color;
}

.search-park-container {
  padding-bottom: 32rpx;
}

/* 搜索框样式 */
.search-input {
  padding: 16rpx 0;
}

.t-class__input-container {
  border-radius: 999rpx !important;
}

.t-search__input {
  font-size: 28rpx !important;
  color: #333 !important;
}

/* 搜索结果列表样式 */
.search-results {
  margin-top: 24rpx;
}

/* 停车场列表样式 - 参考 parking-list */
.parking-list {
  margin-top: 20rpx;
}

/* 停车场卡片样式 */
.parking-item {
  margin-bottom: 30rpx;
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }
}

.parking-card {
  display: flex;
  background-color: @bg-color-white;
  border-radius: 30rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 停车场图片样式 */
.parking-image-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.parking-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.parking-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 停车场信息样式 */
.parking-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 停车场头部信息 */
.parking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.parking-name {
  font-size: 36rpx;
  font-weight: 500;
  color: @gy1;
  line-height: 1.2;
  flex: 1;
}

.parking-type {
  font-size: 22rpx;
  color: @brand7-normal;
  background-color: rgba(0, 82, 217, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  margin-left: auto;
  flex-shrink: 0;
}

.parking-address {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #959595;
  margin-bottom: 8rpx;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.parking-distance {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #959595;
  margin-bottom: 16rpx;

  .distance-info {
    display: flex;
    align-items: center;

    t-icon {
      margin-right: 8rpx;
    }
  }
}

/* 分隔线样式 */
.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 12rpx 0;
}

/* 底部元数据样式 */
.parking-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

/* 余位信息样式 */
.parking-spots {
  display: flex;
  align-items: center;
  font-size: 26rpx;

  .spots-label {
    color: @gy1;
    margin-right: 8rpx;
  }

  .spots-value {
    color: @brand7-normal;
    font-weight: 500;

    &:empty::after {
      content: '未知';
      color: #999999;
    }
  }
}

/* 价格信息样式 */
.parking-price {
  display: flex;
  align-items: baseline;

  .price-value {
    font-size: 30rpx;
    color: @gy1;
    font-weight: 500;

    &:empty::after {
      content: '价格未知';
      color: #999999;
      font-weight: 400;
      font-size: 26rpx;
    }
  }

  .price-unit {
    font-size: 24rpx;
    color: #959595;
    margin-left: 2rpx;
  }
}

/* 历史记录和热门搜索样式 */
.history-wrap, .popular-wrap {
  margin-top: 24rpx;
}

.search-header {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
}

.search-title {
  font-size: 30rpx;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  line-height: 42rpx;
}

.search-clear {
  font-size: 30rpx;
  color: #999999;
}

.search-content {
  overflow: hidden;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 24rpx;
}

.search-item {
  color: #333333;
  font-size: 24rpx;
  line-height: 32rpx;
  font-weight: 400;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
  border-radius: 3rpx;
}

.hover-history-item {
  position: relative;
  top: 3rpx;
  left: 3rpx;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1) inset;
}

.margin-12 {
  margin: 12rpx;
}

.history-item {
  background: #e7e7e7;
}

.popular-item {
  background: #f3f3f3;
}

/* 无搜索结果样式 */
.empty-result {
  margin-top: 120rpx;
  display: flex;
  justify-content: center;
}

/* 加载中样式 */
.loading-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
