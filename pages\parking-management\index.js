import Message from 'tdesign-miniprogram/message/index';
import Dialog from 'tdesign-miniprogram/dialog/index';
import { lockApi, orderApi } from '~/api/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 车位总数
    totalSpots: 0,
    // 车位列表
    parkingList: [],
    // 是否正在加载
    loading: false,
    // 选中的车位
    selectedSpot: {},

    // 共享设置弹窗
    showSharePopup: false,
    // 确认信息弹窗
    showConfirmPopup: false,
    // 时间选择器
    showTimePicker: false,
    // 日期类型选择器
    showDayTypePicker: false,
    // 共享周期选择器
    showCycleTypePicker: false,
    // 当前选择的是开始时间还是结束时间
    currentTimeType: '',
    // 共享开始时间
    shareStartTime: '',
    // 共享结束时间
    shareEndTime: '',
    // 共享结束时间限位器
    shareEndTimeLimit: '',
    // 是否跨天
    isCrossDay: false,
    // 共享时长
    shareDuration: '',
    // 共享单价
    sharePrice: '',
    // 封顶价格
    maxPrice: '',
    // 共享周期类型
    cycleType: 'TODAY',
    // 共享周期类型显示文本
    cycleTypeLabel: '今日',
    // 时间选择器值
    timePickerValue: [],
    // 日期类型选择器值
    dayTypePickerValue: [],
    // 共享周期选择器值
    cycleTypePickerValue: [],
    // 小时选项
    hours: Array.from({ length: 24 }, (_, i) => ({
      label: `${i < 10 ? '0' + i : i}时`,
      value: i
    })),
    // 分钟选项
    minutes: Array.from({ length: 60 }, (_, i) => ({
      label: `${i < 10 ? '0' + i : i}分`,
      value: i
    })),
    // 日期类型选项
    dayTypeOptions: [
      { label: '当日', value: 'NO' },
      { label: '次日', value: 'YES' }
    ],
    // 共享周期选项
    cycleTypeOptions: [
      { label: '每日', value: 'EVERYDAY' },
      { label: '周末', value: 'WEEKEND' },
      { label: '今日', value: 'TODAY' }
    ],

    // 页面滚动位置
    scrollTop: 0,
    // 头部是否折叠
    isHeaderCollapsed: false,
    // 分页相关
    current: 1, // 当前页码，从1开始
    hasMore: true, // 是否有更多数据
    enable: false, // 下拉刷新状态
    loadMoreStatus: 0, // 加载更多状态：0-默认，1-加载中，2-已全部加载，3-加载失败
    refreshTimestamp: 0, // 用于记录最后刷新时间

    // 锁操作弹窗
    lockOperationPopup: {
      visible: false,
      loading: false,
      text: '',
      showRetry: false,
      overlayProps: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
      }
    },

    // 按钮冷却状态
    buttonCooldown: {
      isActive: false,
      remainingTime: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取车位数据
    this.fetchParkingData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // // 页面显示时，确保浮动头部状态正确
    // setTimeout(() => {
    //   this.resetFloatingHeader();
    // }, 300);

    // // 刷新车位数据
    // this.fetchParkingData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    // 重置页码和状态
    this.setData({
      current: 1, // 重置为第1页
      hasMore: true,
      loadMoreStatus: 0, // 重置加载更多状态
      // 刷新时回到顶部
      scrollTop: 0
    });

    this.fetchParkingData().then(() => {
      wx.stopPullDownRefresh();
      // 使用nextTick延迟显示成功消息，避免与刷新动画冲突
      wx.nextTick(() => {
        this.showMessage('刷新成功', 'success');
      });
    }).catch(() => {
      wx.stopPullDownRefresh();
      wx.nextTick(() => {
        this.showMessage('刷新失败', 'error');
      });
    });
  },

  /**
   * 下拉刷新处理函数
   */
  onRefresh() {
    // 如果正在加载中，不执行刷新操作
    if (this.data.loading) return Promise.resolve();

    // 重置页码和状态
    this.setData({
      current: 1, // 重置为第1页
      hasMore: true,
      loadMoreStatus: 0, // 重置加载更多状态
      // 刷新时回到顶部
      scrollTop: 0
    });

    return this.fetchParkingData().then(() => {
      // 使用nextTick延迟显示成功消息，避免与刷新动画冲突
      wx.nextTick(() => {
        this.showMessage('刷新成功', 'success');
      });
    }).catch(() => {
      wx.nextTick(() => {
        this.showMessage('刷新失败', 'error');
      });
    });
  },

  /**
   * 重置浮动头部状态
   */
  resetFloatingHeader() {
    // 根据当前滚动位置决定是否折叠
    const isCollapsed = this.data.scrollTop > 80;

    // 更新状态
    this.setData({ isHeaderCollapsed: isCollapsed });

    // 更新浮动头部组件的折叠状态
    this.updateFloatingHeaderState(isCollapsed);
  },

  /**
   * 获取车位数据
   */
  async fetchParkingData() {
    // 分离加载状态：初始加载使用loading，加载更多只使用loadMoreStatus
    this.setData({
      loading: this.data.current === 1 ? true : false,
      loadMoreStatus: this.data.current > 1 ? 1 : 0 // 如果是加载更多，设置状态为1（加载中）
    });

    try {
      // 获取车位锁数量（只在第一页加载时获取总数）
      let totalSpots = this.data.totalSpots;
      if (this.data.current === 1) {
        const countRes = await lockApi.apiGetLockCount();
        totalSpots = countRes && countRes.code === 0 ? parseInt(countRes.data) || 0 : 0;
      }

      // 获取车位锁列表，使用分页参数
      const lockListRes = await lockApi.apiGetPersonalLockList({
        current: this.data.current,
        size: 10 // 每页10条数据
      });

      if (lockListRes && lockListRes.code === 0 && lockListRes.data) {
        // 获取结果数据
        const results = lockListRes.data.results || [];
        // 处理车位锁列表数据
        const newParkingList = this.processLockList(results);

        // 判断是否有更多数据
        const total = parseInt(lockListRes.data.total) || 0;
        const size = parseInt(lockListRes.data.size) || 10;
        const currentPage = parseInt(lockListRes.data.curPage) || 0;
        const hasMore = total > (currentPage + 1) * size;

        // 更新数据，如果是第一页则替换列表，否则追加到现有列表
        this.setData({
          totalSpots,
          parkingList: this.data.current === 1 ? newParkingList : [...this.data.parkingList, ...newParkingList],
          loading: false,
          hasMore,
          loadMoreStatus: hasMore ? 0 : 2, // 如果没有更多数据，设置状态为2（已全部加载）
          refreshTimestamp: Date.now() // 更新刷新时间戳
        });
      } else {
        // 如果是第一页加载失败，清空列表
        if (this.data.current === 1) {
          this.setData({
            totalSpots: 0,
            parkingList: [],
            loading: false,
            loadMoreStatus: 3 // 设置为加载失败状态
          });
        } else {
          // 如果是加载更多失败，保留现有数据，只更新状态
          this.setData({
            loading: false,
            loadMoreStatus: 3 // 设置为加载失败状态
          });
        }

        this.showMessage('获取车位数据失败', 'error');
        return Promise.reject(new Error('获取车位数据失败'));
      }

      return Promise.resolve();
    } catch (error) {
      console.error('获取车位数据异常:', error);

      // 如果是第一页加载异常，清空列表
      if (this.data.current === 1) {
        this.setData({
          totalSpots: 0,
          parkingList: [],
          loading: false,
          loadMoreStatus: 3 // 设置为加载失败状态
        });
      } else {
        // 如果是加载更多异常，保留现有数据，只更新状态
        this.setData({
          loading: false,
          loadMoreStatus: 3 // 设置为加载失败状态
        });
      }

      this.showMessage('获取车位数据异常，请重试', 'error');
      return Promise.reject(error);
    }
  },

  /**
   * 处理车位锁列表数据
   */
  processLockList(lockList) {
    return lockList.map(item => {
      // 处理共享时间
      const shareTime = item.shareStartTime && item.shareEndTime
        ? `${item.shareStartTime.substring(0, 5)} - ${item.isCrossDay && item.isCrossDay.code === 'YES' ? '次日' : ''}${item.shareEndTime.substring(0, 5)}`
        : '';
      let [shareStartTime, shareEndTime] = ['', '']
      if (item.shareStartTime && item.shareEndTime) {
        shareStartTime = item.shareStartTime
        shareEndTime = item.shareEndTime
      }
      // 处理状态 - 现在我们保留原始状态信息，而不是合并它们
      const isUsing = item.useStatus && item.useStatus.code === 'YES';
      const isSharing = item.shareStatus && item.shareStatus.code === 'YES';

      // 主状态仍然保留用于按钮显示逻辑
      let status = 'unshared';
      if (isUsing) {
        status = 'using';
      } else if (isSharing) {
        status = 'sharing';
      }

      return {
        id: item.id, // 共享方案id
        lockId: item.lockId, // 车锁id
        spotNumber: `${item.location || ''} ${item.code || ''}`, // 车位编号
        status, // 主状态（用于按钮显示逻辑）
        isUsing, // 是否使用中
        isSharing, // 是否共享中
        address: item.parkAddress || '', // 车场地址
        shareStartTime: shareStartTime,
        shareEndTime: shareEndTime,
        shareTime, // 共享时段
        pricePerHour: item.price || '', // 共享单价
        maxPrice: item.capAmount || '', // 封顶价格
        isCrossDay: item.isCrossDay && (item.isCrossDay.code === 'YES'), // 是否跨天
        parkName: item.parkName || '' // 车场名称
      };
    });
  },

  /**
   * 联系用户
   */
  async onContactTap(e) {
    const { id } = e.currentTarget.dataset;
    const spot = this.data.parkingList.find(item => item.id === id);

    if (!spot || !spot.lockId) {
      this.showMessage('车位信息错误', 'error');
      return;
    }

    // 检查车位是否正在被使用
    if (!spot.isUsing) {
      this.showMessage('该车位当前没有用户使用', 'warning');
      return;
    }

    try {
      // 显示加载状态
      this.setData({ loading: true });

      // 获取用户手机号
      const phoneRes = await orderApi.apiGetCustomerPhone({
        lockId: spot.lockId
      });

      this.setData({ loading: false });
      if (phoneRes && phoneRes.code === 0 && phoneRes.data) {
        const phone = phoneRes.data;

        // 确认拨打电话
        wx.makePhoneCall({
          phoneNumber: phone,
          success: () => {
            console.log('拨打电话成功');
          },
          fail: (error) => {
            console.error('拨打电话失败:', error);
            this.showMessage('拨打电话失败，请重试', 'error');
          }
        })
      } else {
        // 获取手机号失败
        const errorMsg = phoneRes && phoneRes.msg ? phoneRes.msg : '获取用户联系方式失败';
        this.showMessage(errorMsg, 'error');
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('获取用户手机号异常:', error);
      this.showMessage('获取用户联系方式失败，请重试', 'error');
    }
  },

  /**
   * 共享车位
   */
  onShareTap(e) {
    const { id } = e.currentTarget.dataset;
    const spot = this.data.parkingList.find(item => item.id === id);
    if (!spot) return;

    // 如果已有共享设置，则使用现有设置作为默认值
    let shareStartTime = '00:00:00';
    let shareEndTime = '00:00:00';
    let isCrossDay = false;
    let shareEndTimeLimit = '23:59:00';
    let startTimeError = false;
    let endTimeError = false;

    if (spot.shareStartTime && spot.shareEndTime) {
      shareStartTime = spot.shareStartTime;
      shareEndTime = spot.shareEndTime;
      // 检查是否包含"次日"标记
      isCrossDay = spot.isCrossDay;
      // 如果跨天则启用限位器
      shareEndTimeLimit = isCrossDay ? shareStartTime : '23:59:00';

      // 验证时间逻辑
      if (shareStartTime && shareEndTime) {
        // 解析时间
        const [startHour, startMinute] = shareStartTime.split(':').map(Number);
        const [endHour, endMinute] = shareEndTime.split(':').map(Number);

        // 计算总分钟数
        const startTotalMinutes = startHour * 60 + startMinute;
        const endTotalMinutes = endHour * 60 + endMinute;

        if (isCrossDay) {
          // 跨天模式：结束时间必须早于开始时间
          if (endTotalMinutes >= startTotalMinutes) {
            endTimeError = true;
          }
        } else {
          // 当日模式：结束时间必须晚于开始时间
          if (endTotalMinutes <= startTotalMinutes) {
            endTimeError = true;
          }
        }
      }
    }

    this.setData({
      selectedSpot: spot,
      showSharePopup: true,
      shareStartTime,
      shareEndTime,
      shareEndTimeLimit,
      isCrossDay,
      startTimeError,
      endTimeError,
      shareDuration: '',
      sharePrice: spot.pricePerHour || '',
      maxPrice: spot.maxPrice || '',
      cycleType: 'EVERYDAY', // 重置为默认值
      cycleTypeLabel: '每日' // 重置为默认标签
    }, () => {
      // 如果有开始和结束时间，计算共享时长
      if (shareStartTime && shareEndTime) {
        this.calculateDuration();
      }

      // 如果有错误，显示提示
      if (endTimeError) {
        const message = isCrossDay ?
          '跨天模式下，结束时间必须早于开始时间' :
          '当日模式下，结束时间必须晚于开始时间';
        this.showMessage(message, 'warning');
      }
    });
  },

  /**
   * 弹窗可见性变化
   */
  onPopupVisibleChange(e) {
    if (!e.detail.visible) {
      this.setData({
        showSharePopup: false
      });
    }
  },

  /**
   * 确认弹窗可见性变化
   */
  onConfirmPopupVisibleChange(e) {
    if (!e.detail.visible) {
      this.setData({
        showConfirmPopup: false
      });
    }
  },

  /**
   * 点击开始时间
   */
  onStartTimeTap() {
    this.setData({
      showTimePicker: true,
      currentTimeType: 'start',
    });
  },
  hidePicker() {
    this.setData({
      showTimePicker: false,
      currentTimeType: '',
    });
  },
  /**
   * 点击结束时间
   */
  onEndTimeTap() {
    this.setData({
      showTimePicker: true,
      currentTimeType: 'end',
    });
  },

  /**
   * 点击日期类型
   */
  onDayTypeTap() {
    this.setData({
      showDayTypePicker: true,
      dayTypePickerValue: [this.data.isCrossDay ? 'YES' : 'NO']
    });
  },

  /**
   * 日期类型选择器变化
   */
  onDayTypeChange(e) {
    const { value } = e.detail;
    const { shareStartTime, shareEndTime } = this.data;
    const isCrossDayNew = value == 'YES';
    const shareEndTimeLimit = isCrossDayNew ? shareStartTime : '23:59:00';

    // 显示提示信息
    if (isCrossDayNew) {
      this.showMessage(`注意!次日截止时间需早于起始时间`, 'info');
    }

    // 检查当前的时间设置是否符合新的日期类型要求
    let endTimeError = false;
    if (shareStartTime && shareEndTime) {
      // 解析时间字符串为小时和分钟
      const [startHour, startMinute] = shareStartTime.split(':').map(Number);
      const [endHour, endMinute] = shareEndTime.split(':').map(Number);

      // 计算总分钟数以便比较
      const startTotalMinutes = startHour * 60 + startMinute;
      const endTotalMinutes = endHour * 60 + endMinute;

      if (isCrossDayNew) {
        // 跨天模式：结束时间必须早于开始时间
        if (endTotalMinutes >= startTotalMinutes) {
          endTimeError = true;
        }
      } else {
        // 当日模式：结束时间必须晚于开始时间
        if (endTotalMinutes <= startTotalMinutes) {
          endTimeError = true;
        }
      }
    }

    this.setData({
      isCrossDay: isCrossDayNew,
      shareEndTimeLimit,
      endTimeError,
      showDayTypePicker: false
    });

    // 如果时间设置不符合要求，显示错误提示
    if (endTimeError) {
      const message = isCrossDayNew ?
        '跨天模式下，结束时间必须早于开始时间' :
        '当日模式下，结束时间必须晚于开始时间';
      this.showMessage(message, 'warning');
    }

    // 如果有开始和结束时间，重新计算共享时长
    if (shareStartTime && shareEndTime) {
      this.calculateDuration();
    }
  },

  /**
   * 点击共享周期类型
   */
  onCycleTypeTap() {
    this.setData({
      showCycleTypePicker: true,
      cycleTypePickerValue: [this.data.cycleType.code]
    });
  },

  /**
   * 共享周期类型选择器变化
   */
  onCycleTypeChange(e) {
    const { value } = e.detail;
    // t-picker 返回的 value 是数组，取第一个元素作为实际值
    const selectedValue = Array.isArray(value) ? value[0] : value;
    // 根据选择的值找到对应的标签
    const selectedOption = this.data.cycleTypeOptions.find(item => item.value === selectedValue);
    const cycleTypeLabel = selectedOption ? selectedOption.label : '未设置';

    this.setData({
      cycleType: selectedValue,
      cycleTypeLabel: cycleTypeLabel,
      showCycleTypePicker: false
    });
  },

  /**
   * 时间选择器变化
   */
  onTimePickerChange(e) {
    const { value } = e.detail;
    const { currentTimeType, shareStartTime, shareEndTime, isCrossDay } = this.data;

    // 准备更新的数据
    const updateData = {
      showTimePicker: false,
      startTimeError: false,
      endTimeError: false
    };

    // 根据当前选择的时间类型更新相应的时间值
    if (currentTimeType === 'start') {
      updateData.shareStartTime = value;

      // 如果是跨天模式且已有结束时间，检查结束时间是否早于新的开始时间
      if (isCrossDay && shareEndTime) {
        // 解析时间
        const [startHour, startMinute] = value.split(':').map(Number);
        const [endHour, endMinute] = shareEndTime.split(':').map(Number);

        // 计算总分钟数
        const startTotalMinutes = startHour * 60 + startMinute;
        const endTotalMinutes = endHour * 60 + endMinute;

        // 跨天模式：结束时间必须早于开始时间
        if (endTotalMinutes >= startTotalMinutes) {
          updateData.endTimeError = true;
        }

        // 更新结束时间限制
        updateData.shareEndTimeLimit = value;
      }
    } else {
      updateData.shareEndTime = value;

      // 如果已有开始时间，检查时间逻辑
      if (shareStartTime) {
        // 解析时间
        const [startHour, startMinute] = shareStartTime.split(':').map(Number);
        const [endHour, endMinute] = value.split(':').map(Number);

        // 计算总分钟数
        const startTotalMinutes = startHour * 60 + startMinute;
        const endTotalMinutes = endHour * 60 + endMinute;

        if (isCrossDay) {
          // 跨天模式：结束时间必须早于开始时间
          if (endTotalMinutes >= startTotalMinutes) {
            updateData.endTimeError = true;
          }
        } else {
          // 当日模式：结束时间必须晚于开始时间
          if (endTotalMinutes <= startTotalMinutes) {
            updateData.endTimeError = true;
          }
        }
      }
    }

    // 更新数据
    this.setData(updateData, () => {
      // 显示错误提示
      if (updateData.endTimeError) {
        const message = isCrossDay ?
          '跨天模式下，结束时间必须早于开始时间' :
          '当日模式下，结束时间必须晚于开始时间';
        this.showMessage(message, 'warning');
      }

      // 如果有开始和结束时间，计算共享时长
      if (this.data.shareStartTime && this.data.shareEndTime) {
        this.calculateDuration();
      }
    });
  },

  /**
   * 计算共享时长
   */
  calculateDuration() {
    const { shareStartTime, shareEndTime, isCrossDay } = this.data;
    if (!shareStartTime || !shareEndTime) return;

    // 解析时间
    const [startHour, startMinute] = shareStartTime.split(':').map(Number);
    const [endHour, endMinute] = shareEndTime.split(':').map(Number);

    // 计算总分钟数
    const startTotalMinutes = startHour * 60 + startMinute;
    const endTotalMinutes = endHour * 60 + endMinute;

    // 计算时长（分钟）
    let durationMinutes;

    if (isCrossDay) {
      // 跨天模式：结束时间应该早于开始时间，所以需要加上24小时
      durationMinutes = (endTotalMinutes + 24 * 60) - startTotalMinutes;
    } else {
      // 当日模式：结束时间应该晚于开始时间
      durationMinutes = endTotalMinutes - startTotalMinutes;
      // 如果计算结果为负数（可能是时间设置有误），加上24小时
      if (durationMinutes < 0) {
        durationMinutes += 24 * 60;
      }
    }

    // 转换为小时和分钟
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    // 更新显示
    this.setData({
      shareDuration: `共享时长：${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
    });
  },

  /**
   * 价格变化
   */
  onPriceChange(e) {
    this.setData({
      sharePrice: e.detail.value
    });
  },

  /**
   * 封顶价格变化
   */
  onMaxPriceChange(e) {
    this.setData({
      maxPrice: e.detail.value
    });
  },

  /**
   * 取消共享
   */
  onCancelShare() {
    this.setData({
      showSharePopup: false
    });
  },

  /**
   * 下一步
   */
  onNextStep() {
    // 表单验证
    if (!this.validateShareForm()) {
      return;
    }

    // 显示确认弹窗
    this.setData({
      showSharePopup: false,
      showConfirmPopup: true
    });
  },

  /**
   * 验证共享表单
   */
  validateShareForm() {
    const { shareStartTime, shareEndTime, sharePrice, maxPrice, isCrossDay } = this.data;
    // 重置所有表单项的错误状态
    this.setData({
      startTimeError: false,
      endTimeError: false,
      priceError: false,
      maxPriceError: false
    });

    let isValid = true;
    let errors = {};

    // 验证必填项
    if (!shareStartTime) {
      this.showMessage('请选择共享开始时间', 'warning');
      errors.startTimeError = true;
      isValid = false;
    }

    if (!shareEndTime) {
      this.showMessage('请选择共享结束时间', 'warning');
      errors.endTimeError = true;
      isValid = false;
    }

    // 验证跨天时间逻辑 - 增强验证
    if (shareStartTime && shareEndTime) {
      // 解析时间字符串为小时和分钟
      const [startHour, startMinute] = shareStartTime.split(':').map(Number);
      const [endHour, endMinute] = shareEndTime.split(':').map(Number);

      // 计算总分钟数以便比较
      const startTotalMinutes = startHour * 60 + startMinute;
      const endTotalMinutes = endHour * 60 + endMinute;

      if (isCrossDay) {
        // 当跨天时，结束时间必须早于开始时间
        if (endTotalMinutes >= startTotalMinutes) {
          this.showMessage('跨天模式下，结束时间必须早于开始时间', 'warning');
          errors.endTimeError = true;
          isValid = false;
        }
      } else {
        // 当不跨天时，结束时间应该晚于开始时间
        if (endTotalMinutes <= startTotalMinutes) {
          this.showMessage('当日模式下，结束时间必须晚于开始时间', 'warning');
          errors.endTimeError = true;
          isValid = false;
        }
      }
    }

    if (!sharePrice) {
      this.showMessage('请输入共享单价', 'warning');
      errors.priceError = true;
      isValid = false;
    }

    if (!maxPrice) {
      this.showMessage('请输入封顶价格', 'warning');
      errors.maxPriceError = true;
      isValid = false;
    }

    // 验证价格
    const priceValue = parseFloat(sharePrice);
    const maxPriceValue = parseFloat(maxPrice);

    if (isNaN(priceValue) || priceValue <= 0) {
      this.showMessage('共享单价必须大于0', 'warning');
      errors.priceError = true;
      isValid = false;
    }

    if (isNaN(maxPriceValue) || maxPriceValue <= 0) {
      this.showMessage('封顶价格必须大于0', 'warning');
      errors.maxPriceError = true;
      isValid = false;
    }

    if (priceValue > maxPriceValue) {
      this.showMessage('共享单价不能高于封顶价格', 'warning');
      errors.priceError = true;
      errors.maxPriceError = true;
      isValid = false;
    }

    // 一次性更新所有错误状态，减少setData调用次数
    if (Object.keys(errors).length > 0) {
      this.setData(errors);
    }

    return isValid;
  },

  /**
   * 返回共享设置
   */
  onBackToShare() {
    this.setData({
      showConfirmPopup: false,
      showSharePopup: true
    });
  },

  /**
   * 仅保存不共享
   */
  onSaveOnly() {
    // 表单验证
    if (!this.validateShareForm()) {
      return;
    }

    this.updateShareStatus(false);
  },

  /**
   * 保存并立刻共享
   */
  onStartSharing() {
    this.updateShareStatus(true);
  },

  /**
   * 更新共享状态
   */
  async updateShareStatus(startSharing = true) {
    this.setData({
      loading: true,
      showConfirmPopup: false,
      showSharePopup: false
    });

    try {
      const { selectedSpot, shareStartTime, shareEndTime, sharePrice, maxPrice, isCrossDay, cycleType } = this.data;
      // 调用API更新共享设置
      const updateRes = await lockApi.apiUpdateShareRule({
        id: selectedSpot.id,
        price: sharePrice,
        shareStartTime: `${shareStartTime}`,
        shareEndTime: `${shareEndTime}`,
        isCrossDay: isCrossDay ? 'YES' : 'NO',
        // shareStatus: startSharing ? 'YES' : 'NO', // 是否立即共享
        cycleType: cycleType,
        illegalMultiple: 2, // 默认违规倍数
        capAmount: maxPrice
      });

      if (updateRes && updateRes.code === 0) {
        // 是否立即共享
        await lockApi.apiUpdateShareStatus({
          id: selectedSpot.id,
          shareStatus: (!!startSharing) ? 'YES' : 'NO'
        })
        // 更新车位状态
        setTimeout(() => {
          this.fetchParkingData();
          this.setData({
            loading: false,
            // 清空表单数据
            shareStartTime: '',
            shareEndTime: '',
            shareDuration: '',
            sharePrice: '',
            maxPrice: '',
            isCrossDay: false,
            cycleType: 'EVERYDAY',
            cycleTypeLabel: '每日'
          });
        }, 1000);
        this.showMessage(startSharing ? '车位共享设置成功' : '车位设置已保存', 'success');
      } else {
        this.setData({ loading: false });
        this.showMessage(startSharing ? '车位共享设置失败' : '车位设置保存失败', 'error');
      }
    } catch (error) {
      console.error('车位设置异常:', error);
      this.setData({ loading: false });
      this.showMessage('操作异常，请重试', 'error');
    }
  },

  /**
   * 加载更多数据
   */
  loadMore() {
    if (!this.data.hasMore || this.data.loading || this.data.loadMoreStatus === 1) return;

    // 增加页码
    const nextPage = this.data.current + 1;

    // 只更新loadMoreStatus状态，不触发整个页面的loading状态
    this.setData({
      current: nextPage,
      loadMoreStatus: 1 // 设置为加载中状态
    }, () => {
      // 在页码更新后加载下一页数据
      this.fetchParkingData().then(() => {
        // 静默加载，不显示成功消息
      }).catch(() => {
        // 如果加载失败，恢复之前的页码
        wx.nextTick(() => {
          this.setData({
            current: this.data.current - 1,
            loadMoreStatus: 3 // 设置为加载失败状态
          });
          this.showMessage('加载更多失败', 'error');
        });
      });
    });
  },

  /**
   * 页面滚动到底部时触发
   */
  onReachBottom() {
    // 防止重复触发，增加节流处理
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
    }

    this._reachBottomTimer = setTimeout(() => {
      if (this.data.hasMore && !this.data.loading && this.data.loadMoreStatus !== 1) {
        this.loadMore();
      }
    }, 200); // 200ms的节流时间
  },

  /**
   * 重试加载
   */
  onRetryLoad() {
    if (this.data.loadMoreStatus === 3) {
      this.loadMore();
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    // 清理定时器
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
      this._reachBottomTimer = null;
    }
  },

  /**
   * 页面滚动事件的处理函数
   */
  onPageScroll(e) {
    // 保存滚动位置
    const scrollTop = e.scrollTop;
    this.data.scrollTop = scrollTop; // 直接更新数据对象，不触发setData以避免频繁渲染
    const isCollapsed = scrollTop > 80;

    // 只有当折叠状态发生变化时才更新数据，避免频繁更新
    if (isCollapsed !== this.data.isHeaderCollapsed) {
      this.setData({
        scrollTop: scrollTop,
        isHeaderCollapsed: isCollapsed
      });

      // 更新浮动头部组件的折叠状态
      this.updateFloatingHeaderState(isCollapsed);
    } else {
      this.setData({ scrollTop: scrollTop });
    }
  },

  /**
   * 更新浮动头部组件的折叠状态
   */
  updateFloatingHeaderState(isCollapsed) {
    const query = wx.createSelectorQuery();
    query.select('#floatingHeader').node();
    query.exec((res) => {
      if (res && res[0] && res[0].node) {
        const headerComponent = res[0].node.component;
        if (headerComponent && typeof headerComponent.setCollapsed === 'function') {
          headerComponent.setCollapsed(isCollapsed);
        }
      }
    });
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 升锁/降锁操作
   */
  async onLockOperateTap(e) {
    // 检查按钮是否在冷却中
    if (this.data.buttonCooldown.isActive) {
      this.showMessage(`请等待 ${this.data.buttonCooldown.remainingTime} 秒后再操作`, 'warning');
      return;
    }

    const { id } = e.currentTarget.dataset;
    const spot = this.data.parkingList.find(item => item.id === id);

    if (!spot || !spot.lockId) {
      this.showMessage('车位信息错误', 'error');
      return;
    }

    try {
      // 1. 检查当前锁状态
      this.showLockOperationPopup('正在检查锁状态，请稍候...', true);

      const statusRes = await lockApi.apiCheckLockStatus({ lockId: spot.lockId });

      if (!statusRes || statusRes.code !== 0) {
        this.hideLockOperationPopup();
        this.showMessage('检查锁状态失败，请重试', 'error');
        return;
      }

      const currentStatus = statusRes.data.code;
      if (!currentStatus) {
        this.hideLockOperationPopup();
        this.showMessage('获取锁状态失败，请重试', 'error');
        return;
      }

      // 2. 确定操作类型
      const isOpen = currentStatus === 'OPEN';
      const operationType = isOpen ? 'CLOSE' : 'OPEN';

      // 3. 执行锁操作
      this.updateLockOperationPopup(`正在发送，请稍候...`, true);

      const operateRes = await lockApi.apiOperateLockWithUser({
        lockId: spot.lockId,
        status: operationType
      });

      if (!operateRes || operateRes.code !== 0) {
        this.hideLockOperationPopup();
        this.showMessage('操作失败，请重试', 'error');
        return;
      }

      // 4. 等待5秒
      this.updateLockOperationPopup(`操作指令已发送，请等待...`, true);

      await this.delay(5000);

      // 5秒后直接隐藏弹窗，不再验证锁状态
      this.hideLockOperationPopup();

      // 启动按钮冷却
      this.startButtonCooldown();

      // 刷新车位列表
      setTimeout(() => {
        this.fetchParkingData();
      }, 1000);

    } catch (error) {
      // 处理异常
      console.error('锁操作异常:', error);
      this.hideLockOperationPopup();
      this.showMessage('操作异常，请重试', 'error');
    }
  },

  /**
   * 显示锁操作弹窗
   */
  showLockOperationPopup(text, loading = false) {
    this.setData({
      'lockOperationPopup.visible': true,
      'lockOperationPopup.loading': loading,
      'lockOperationPopup.text': text,
      'lockOperationPopup.showRetry': false
    });
  },

  /**
   * 更新锁操作弹窗
   */
  updateLockOperationPopup(text, loading = false) {
    this.setData({
      'lockOperationPopup.loading': loading,
      'lockOperationPopup.text': text,
      'lockOperationPopup.showRetry': false
    });
  },

  /**
   * 隐藏锁操作弹窗
   */
  hideLockOperationPopup() {
    this.setData({
      'lockOperationPopup.visible': false,
      'lockOperationPopup.loading': false,
      'lockOperationPopup.text': '',
      'lockOperationPopup.showRetry': false
    });
  },

  /**
   * 显示锁操作错误
   */
  showLockOperationError(text, lockId) {
    this.setData({
      'lockOperationPopup.loading': false,
      'lockOperationPopup.text': text,
      'lockOperationPopup.showRetry': true,
      'lockOperationPopup.lockId': lockId
    });
  },

  /**
   * 重试锁操作
   */
  onRetryLockOperation() {
    // 检查按钮是否在冷却中
    if (this.data.buttonCooldown.isActive) {
      this.showMessage(`请等待 ${this.data.buttonCooldown.remainingTime} 秒后再操作`, 'warning');
      return;
    }

    const lockId = this.data.lockOperationPopup.lockId;
    if (lockId) {
      this.hideLockOperationPopup();
      // 模拟点击事件重新执行操作
      const spot = this.data.parkingList.find(item => item.lockId === lockId);
      if (spot) {
        this.onLockOperateTap({ currentTarget: { dataset: { id: spot.id } } });
      }
    }
  },

  /**
   * 取消锁操作
   */
  onCancelLockOperation() {
    this.hideLockOperationPopup();
  },

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  },

  /**
   * 启动按钮冷却
   */
  startButtonCooldown() {
    this.setData({
      'buttonCooldown.isActive': true,
      'buttonCooldown.remainingTime': 30
    });

    // 启动倒计时
    this.cooldownTimer = setInterval(() => {
      const remainingTime = this.data.buttonCooldown.remainingTime - 1;

      if (remainingTime <= 0) {
        // 冷却结束
        this.setData({
          'buttonCooldown.isActive': false,
          'buttonCooldown.remainingTime': 0
        });
        clearInterval(this.cooldownTimer);
        this.cooldownTimer = null;
      } else {
        // 更新倒计时
        this.setData({
          'buttonCooldown.remainingTime': remainingTime
        });
      }
    }, 1000);
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    // 清理冷却定时器
    if (this.cooldownTimer) {
      clearInterval(this.cooldownTimer);
      this.cooldownTimer = null;
    }
  }
});
