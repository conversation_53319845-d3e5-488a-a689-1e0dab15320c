<!-- components/plate-manager/index.wxml -->
<t-popup visible="{{visible}}" placement="bottom" bind:visible-change="close">
  <view class="plate-manager">
    <view class="plate-manager__header">
      <view class="plate-manager__title">车牌管理</view>
      <t-icon name="close" size="44rpx" color="#999999" bindtap="close" />
    </view>
    
    <view class="plate-manager__content">
      <t-loading theme="circular" size="40rpx" loading="{{loading}}" class="plate-manager__loading" />
      
      <block wx:if="{{!loading}}">
        <block wx:if="{{plateList.length > 0}}">
          <view
            class="plate-item {{item.plateNo === selectedPlate ? 'active' : ''}}"
            wx:for="{{plateList}}"
            wx:key="id"
            bindtap="selectPlate"
            data-plate="{{item}}"
          >
            <view class="plate-info">
              <view class="plate-text">{{item.plateNo}}</view>
              <view wx:if="{{item.isDefault}}" class="default-tag">默认</view>
            </view>
            <view class="plate-actions">
              <t-icon wx:if="{{item.plateNo === selectedPlate}}" name="check" size="44rpx" color="#0052D9" />
              <t-icon 
                name="delete" 
                size="44rpx" 
                color="#FA5151" 
                catchtap="deletePlate"
                data-plate="{{item}}"
              />
            </view>
          </view>
        </block>
        
        <view wx:else class="empty-plate-list">
          <t-icon name="info-circle" size="80rpx" color="#CCCCCC" />
          <view class="empty-text">暂无车牌，请添加</view>
        </view>
      </block>
    </view>
    
    <view class="plate-manager__footer">
      <view class="add-plate-btn" bindtap="showAddPlatePopup">
        <t-icon name="add" size="36rpx" color="#0052D9" />
        <text>添加新车牌</text>
      </view>
    </view>
  </view>
</t-popup>

<!-- 添加车牌弹窗 -->
<t-popup visible="{{showAddPlatePopup}}" placement="bottom" bind:visible-change="closeAddPlatePopup"
>
  <view class="add-plate-popup">
    <view class="add-plate-popup__header">
      <view class="add-plate-popup__title">添加车牌</view>
      <t-icon name="close" size="44rpx" color="#999999" bindtap="closeAddPlatePopup" />
    </view>
    
    <view class="add-plate-popup__content">
      <t-input
        label="车牌号"
        placeholder="请输入车牌号"
        value="{{newPlate}}"
        bind:change="onPlateInput"
        maxcharacter="20"
        style="margin-bottom: 20rpx;"
      />
      
      <view class="plate-format-hint">
        <t-icon name="info-circle" size="32rpx" color="#999999" />
        <text>格式示例：浙A12345</text>
      </view>
    </view>
    
    <view class="add-plate-popup__footer">
      <t-button theme="primary" block size="large" bindtap="addNewPlate" loading="{{loading}}">确认添加</t-button>
    </view>
  </view>
</t-popup>
