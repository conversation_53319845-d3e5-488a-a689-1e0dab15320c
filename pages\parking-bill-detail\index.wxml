<view class="bill-detail-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="账单详情" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header>
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="bill-info-main">
      <view class="bill-status-text">{{billInfo.status.desc}}</view>
      <view class="bill-amount">
        <text class="amount-label">实付款：</text>
        <text class="amount-value">￥{{billInfo.realPayAmount || '0.00'}}</text>
      </view>
      <view wx:if="{{billInfo.status.code === 'PARTIALLY_PAID'}}" class="bill-unpaid">
        <text class="unpaid-label">待补缴：</text>
        <text class="unpaid-value">￥{{billInfo.unpaidAmount || '0.00'}}</text>
      </view>
    </view>

    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary" class="bill-info-secondary">
      <view class="bill-info-row">
        <view class="bill-info-item">
          <view class="info-label">
            <t-icon name="money" size="40rpx" color="#ffffff" />
            <text class="label-text">订单金额</text>
          </view>
          <view class="info-value">
            <text class="value-text">￥{{billInfo.amount || '0.00'}}</text>
          </view>
        </view>

        <view class="info-divider"></view>

        <view class="bill-info-item">
          <view class="info-label">
            <t-icon name="discount" size="40rpx" color="#ffffff" />
            <text class="label-text">优惠金额</text>
          </view>
          <view class="info-value">
            <text class="value-text">￥{{billInfo.preferentialAmount || '0.00'}}</text>
          </view>
        </view>
      </view>
    </view>
  </floating-header>

  <!-- 可滚动的内容区域 -->
  <view class="bill-detail-content">
    <!-- 车位信息卡片 -->
    <view class="card">
      <view class="card-title">车位信息</view>
      <view class="info-item">
        <view class="info-label">车场：</view>
        <view class="info-value">{{billInfo.parkName || '暂无'}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">地址：</view>
        <view class="info-value">{{billInfo.parkAddress || '暂无'}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">车位号：</view>
        <view class="info-value">{{billInfo.location || '暂无'}}{{billInfo.code ? ' - ' + billInfo.code : ''}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">车牌号：</view>
        <view class="info-value">{{billInfo.plateNo || '暂无'}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">车位单价：</view>
        <view class="info-value">￥{{billInfo.price || '0.00'}}/小时</view>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <view class="info-label">订单编号：</view>
        <view class="info-value order-no">
          <text>{{billInfo.orderNo || 'ORD-20250518-001'}}</text>
          <view class="copy-btn" bindtap="copyOrderNo">复制</view>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">下单时间：</view>
        <view class="info-value">{{billInfo.orderTime || '暂无'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">总停时长：</view>
        <view class="info-value">{{billInfo.duration || '暂无'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">停车状态：</view>
        <view class="info-value">
          <text class="{{billInfo.parkingStatus.code === 'NORMAL' ? 'status-normal' : 'status-warning'}}">{{billInfo.parkingStatus.desc || '未超时'}}</text>
        </view>
      </view>

      <!-- 可折叠的详细信息 -->
      <view class="collapsible-section {{showDetails ? 'expanded' : ''}}">
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">入场时间：</view>
          <view class="info-value">{{billInfo.arriveDatetime || ''}}</view>
        </view>
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">开锁时间：</view>
          <view class="info-value">{{billInfo.lockOpenTime || ''}}</view>
        </view>
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">关锁时间：</view>
          <view class="info-value">{{billInfo.lastLockCloseTime || ''}}</view>
        </view>
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">出场时间：</view>
          <view class="info-value">{{billInfo.leaveDatetime || ''}}</view>
        </view>
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">支付时间：</view>
          <view class="info-value">{{billInfo.paySuccessDatetime || ''}}</view>
        </view>
        <view class="divider"></view>
        <view class="info-item">
          <view class="info-label">备注：</view>
          <view class="info-value">{{billInfo.remark || '正常停车'}}</view>
        </view>
      </view>

      <view class="expand-details" bindtap="toggleDetails">
        <text>{{showDetails ? '收起详情' : '展开详情'}}</text>
        <t-icon name="{{showDetails ? 'chevron-up' : 'chevron-down'}}" size="32rpx" color="#959595" />
      </view>
    </view>

    <!-- 费用信息卡片 -->
    <view class="card">
      <view class="card-title">费用信息</view>
      <view class="info-item">
        <view class="info-label">订单金额：</view>
        <view class="info-value">￥{{billInfo.amount || '13.00'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">停车费用：</view>
        <view class="info-value">￥{{billInfo.parkAmount || '13.00'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item" wx:if="{{billInfo.timeoutAmount && billInfo.timeoutAmount !== '0.00'}}">
        <view class="info-label">超时费用：</view>
        <view class="info-value status-warning">￥{{billInfo.timeoutAmount || '0.00'}}</view>
      </view>
      <view class="divider" wx:if="{{billInfo.timeoutAmount && billInfo.timeoutAmount !== '0.00'}}"></view>
      <view class="info-item">
        <view class="info-label">优惠金额：</view>
        <view class="info-value status-success">-￥{{billInfo.preferentialAmount || '4.00'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item highlight-item">
        <view class="info-label">应付金额：</view>
        <view class="info-value">￥{{billInfo.payableAmount || '9.00'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item highlight-item" >
        <view class="info-label">补缴金额：</view>
        <view class="info-value status-warning">￥{{billInfo.unpaidAmount || '5.00'}}</view>
      </view>
      <view class="divider"></view>
      <view class="info-item highlight-item">
        <view class="info-label">实付金额：</view>
        <view class="info-value status-large" >￥{{billInfo.realPayAmount || '9.00'}}</view>
      </view>
    </view>

    <!-- 退款信息卡片 -->
    <view class="card" wx:if="{{billInfo.status.code === 'REFUNDED' || billInfo.status.code === 'REFUNDING'}}">
      <view class="refund-info">
        <t-icon name="info-circle" size="32rpx" color="#959595" />
        <text>剩余保证金将在10分钟内原路退回</text>
      </view>
    </view>

    <!-- 申请退款按钮 -->
    <view class="refund-button-container" wx:if="{{billInfo.status.code === 'COMPLETED' && !billInfo.isRefunded}}">
      <view class="refund-button" bindtap="onApplyRefund">
        <text>申请退款</text>
      </view>
    </view>
  </view>

  <!-- 底部浮动栏 - 仅在部分支付状态下显示 -->
  <view class="bottom-bar" wx:if="{{billInfo.status.code === 'PARTIALLY_PAID'}}">
    <t-button
      theme="primary"
      size="large"
      bindtap="onPaySupplementFee"
      class="order-button"
    >支付补缴 ￥{{billInfo.unpaidAmount || '5.00'}}</t-button>
  </view>

  <!-- 退款确认弹窗 -->
  <t-dialog
    visible="{{showRefundDialog}}"
    title="申请退款"
    confirm-btn="{{ { content: '确认申请', variant: 'base' } }}"
    cancel-btn="{{ { content: '取消' } }}"
    bind:confirm="onConfirmRefund"
    bind:cancel="onCancelRefund"
  >
    <view slot="content">确定要申请退款吗？退款申请提交后，系统将在1-3个工作日内处理。</view>
  </t-dialog>

  <!-- 消息提示 -->
  <t-message id="t-message" />
</view>
