import Message from 'tdesign-miniprogram/message/index';
import { parkApi } from '~/api/index';
import { formatDistance } from '~/utils/util'
Page({
  data: {
    enable: false,
    loading: true,
    parkingList: [],
    current: 1, // 当前页码，从1开始
    hasMore: true, // 是否有更多数据
    refreshTimestamp: 0, // 用于记录最后刷新时间
    scrollTop: 0, // 记录滚动位置
    loadMoreStatus: 0, // 加载更多状态：0-默认，1-加载中，2-已全部加载，3-加载失败
  },

  onLoad(options) {
    // 如果有ID参数，可以用于筛选特定区域的停车场
    const { id } = options;
    if (id) {
      this.setData({ areaId: id });
    }

    // 获取并排序停车场列表
    this.fetchParkingList();

    // 注册 t-message 组件
    this.message = this.selectComponent('#t-message');
  },

  onReady() {
    // 页面初次渲染完成
  },

  onShow() {
    // 页面显示
  },

  // 返回按钮点击事件
  onBackTap() {
    wx.navigateBack({
      fail: () => {
        wx.switchTab({
          url: '/pages/home-park/index'
        });
      }
    });
  },

  // 下拉刷新
  onRefresh() {
    // 如果正在加载中，不执行刷新操作
    if (this.data.loading) return Promise.resolve();

    // 重置页码和状态
    this.setData({
      current: 1, // 重置为第1页，因为API是从1开始计数
      hasMore: true,
      loadMoreStatus: 0, // 重置加载更多状态
      // 刷新时回到顶部
      scrollTop: 0
    });

    return this.fetchParkingList().then(() => {
      // 不需要显示刷新成功消息，因为fetchParkingList已经显示了排序成功消息
      // 避免显示两次成功消息
    }).catch(() => {
      wx.nextTick(() => {
        this.showMessage('刷新失败', 'error');
      });
    });
  },
  // 获取停车场列表
  fetchParkingList() {
    // 分离加载状态：初始加载使用loading，加载更多只使用loadMoreStatus
    this.setData({
      loading: this.data.current === 1 ? true : false,
      loadMoreStatus: this.data.current > 1 ? 1 : 0 // 如果是加载更多（current > 1），设置状态为1（加载中）
    });

    return new Promise((resolve, reject) => {
      // 使用apiSearchParks获取数据，current从1开始
      parkApi.apiSearchParks({
        current: this.data.current
      })
        .then(res => {
          if (res && res.code === 0 && res.data) {
            // 检查是否有结果数据
            const results = res.data.results || [];

            const newParkingList = results.map(item => ({
              id: item.id,
              name: item.parkName,
              address: item.parkAddress,
              distance: formatDistance(item.distance),
              averagePrice: item.averagePrice || '0', // 如果没有均价信息，使用默认值
              availableSpots: item.availableSlots || 0,
              parkType: item.parkType ? item.parkType.desc : '普通停车场',
              image: item['image'] ? item.image : ''
            }));

            // 判断是否有更多数据
            const totalPages = parseInt(res.data.pages) || 0;
            // 使用当前页码判断是否有更多数据
            const hasMore = this.data.current < totalPages;

            // 根据可用车位数量进行降序排序
            let parkingListToSort = this.data.current === 1
              ? newParkingList
              : this.data.parkingList.concat(newParkingList);

            // 对停车场列表进行排序（按可用车位数降序）
            parkingListToSort = parkingListToSort.sort((a, b) => {
              // 将 availableSpots 转换为数字进行比较
              const spotsA = parseInt(a.availableSpots) || 0;
              const spotsB = parseInt(b.availableSpots) || 0;
              return spotsB - spotsA; // 降序排序
            });

            // 使用nextTick延迟状态更新，减少渲染次数
            wx.nextTick(() => {
              this.setData({
                parkingList: parkingListToSort,
                hasMore,
                // 设置加载更多状态
                loadMoreStatus: hasMore ? 0 : 2, // 0-默认，2-已全部加载
                loading: false
              });

              // 如果是第一页，显示排序成功的消息
              // if (this.data.current === 1) {
              //   this.showMessage('已按可用车位数排序', 'success');
              // }

              resolve();
            });
          } else {
            wx.nextTick(() => {
              this.setData({
                loading: false,
                loadMoreStatus: this.data.current > 1 ? 3 : 0 // 如果是加载更多（current > 1），设置状态为3（加载失败）
              });
              reject(new Error('获取停车场列表失败'));
            });
          }
        })
        .catch(error => {
          wx.nextTick(() => {
            this.setData({
              loading: false,
              loadMoreStatus: this.data.current > 1 ? 3 : 0 // 如果是加载更多（current > 1），设置状态为3（加载失败）
            });
            this.showMessage('获取停车场列表失败', 'error');
            reject(error);
          });
        });
    });
  },


  // 点击停车场
  onParkingTap(e) {
    const { id } = e.currentTarget.dataset;
    const parking = this.data.parkingList.find(item => item.id === id);
    console.log(parking);
    if (!parking) {
      this.showMessage('停车场信息不存在', 'error');
      return;
    }

    // 导航到停车场详情页
    wx.navigateTo({
      url: `/pages/parking-spots/index?id=${id}`
    });
  },

  // 加载更多数据
  loadMore() {
    if (!this.data.hasMore || this.data.loading || this.data.loadMoreStatus === 1) return;

    // 增加页码，API是从1开始计数的
    const nextPage = this.data.current + 1;

    // 只更新loadMoreStatus状态，不触发整个页面的loading状态
    this.setData({
      current: nextPage,
      loadMoreStatus: 1 // 设置为加载中状态
    }, () => {
      // 在页码更新后加载下一页数据
      this.fetchParkingList().then(() => {
        // 静默加载，不显示成功消息
      }).catch(() => {
        // 如果加载失败，恢复之前的页码
        wx.nextTick(() => {
          this.setData({
            current: this.data.current - 1,
            loadMoreStatus: 3 // 设置为加载失败状态
          });
          this.showMessage('加载更多失败', 'error');
        });
      });
    });
  },


  // 页面滚动事件处理函数
  onPageScroll(e) {
    // 记录滚动位置，但不触发setData以避免重新渲染
    this.data.scrollTop = e.scrollTop;
  },

  // 页面滚动到底部时触发
  onReachBottom() {
    // 防止重复触发，增加节流处理
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
    }

    this._reachBottomTimer = setTimeout(() => {
      if (this.data.hasMore && !this.data.loading && this.data.loadMoreStatus !== 1) {
        this.loadMore();
      }
    }, 200); // 200ms的节流时间
  },

  // 重试加载
  onRetryLoad() {
    if (this.data.loadMoreStatus === 3) {
      this.loadMore();
    }
  },

  // 页面卸载时清理资源
  onUnload() {
    // 清理定时器
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
      this._reachBottomTimer = null;
    }
  },

  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
