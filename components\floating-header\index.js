Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 自定义类名
    customClass: {
      type: String,
      value: ''
    },
    // 是否启用滚动折叠
    enableCollapse: {
      type: Boolean,
      value: true
    },
    // 折叠阈值（滚动多少距离开始折叠）
    collapseThreshold: {
      type: Number,
      value: 100
    },
    // 展开时的高度（用于占位元素）
    expandedHeight: {
      type: Number,
      value: 0
    },
    // 折叠时的高度（用于占位元素）
    collapsedHeight: {
      type: Number,
      value: 180
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isCollapsed: false,
    placeholderHeight: 0,
    scrollListener: null
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      // 计算初始高度
      this.calculateHeight();

      // 如果启用折叠，添加页面滚动监听
      if (this.properties.enableCollapse) {
        this.setupScrollListener();
      }
    },

    ready() {
      // 组件完全准备好后，再次计算高度，确保准确
      setTimeout(() => {
        this.calculateHeight();

        // 初始化次要内容区域
        if (this.data.isCollapsed) {
          this.forceUpdateSecondaryContent();
        }
      }, 300);
    },

    detached() {
      // 移除滚动监听
      this.removeScrollListener();
    }
  },

  /**
   * 属性监听器
   */
  observers: {
    'enableCollapse': function (newVal) {
      if (newVal) {
        this.setupScrollListener();
      } else {
        this.removeScrollListener();
        // 如果禁用折叠，确保展开状态
        this.setData({ isCollapsed: false });
        this.updatePlaceholderHeight();
      }
    },
    'collapseThreshold': function () {
      // 阈值变化时，重新检查折叠状态
      if (this.properties.enableCollapse) {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage._lastScrollTop !== undefined) {
          // 使用页面上次记录的滚动位置重新判断
          const e = { scrollTop: currentPage._lastScrollTop };
          this.data.scrollListener && this.data.scrollListener(e);
        }
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 设置滚动监听
    setupScrollListener() {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 设置页面的滚动监听函数
      const scrollListener = (e) => {
        const scrollTop = e.scrollTop;

        // 记录滚动位置到页面实例，方便后续使用
        if (currentPage) {
          currentPage._lastScrollTop = scrollTop;
        }

        // 根据滚动位置决定是否折叠
        if (scrollTop > this.properties.collapseThreshold && !this.data.isCollapsed) {
          this.setData({ isCollapsed: true }, () => {
            // 状态变化后立即更新占位元素高度
            this.updatePlaceholderHeight();
            // 强制重新渲染次要内容区域
            this.forceUpdateSecondaryContent();
          });
        } else if (scrollTop <= this.properties.collapseThreshold && this.data.isCollapsed) {
          this.setData({ isCollapsed: false }, () => {
            // 状态变化后立即更新占位元素高度
            this.updatePlaceholderHeight();
            // 强制重新渲染次要内容区域
            this.forceUpdateSecondaryContent();
          });
        }
      };

      // 保存监听器引用
      this.setData({ scrollListener });

      // 设置页面滚动监听
      if (currentPage) {
        // 保存原有的 onPageScroll（如果存在）
        const originalOnPageScroll = currentPage.onPageScroll;

        // 重写 onPageScroll
        currentPage.onPageScroll = (e) => {
          // 调用原有的 onPageScroll（如果存在）
          if (typeof originalOnPageScroll === 'function') {
            originalOnPageScroll.call(currentPage, e);
          }
          // 调用组件的滚动监听
          scrollListener(e);
        };
      }
    },

    // 移除滚动监听
    removeScrollListener() {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 如果有滚动监听器，尝试移除
      if (currentPage && currentPage.onPageScroll) {
        // 不直接设置为 null，而是设置为一个空函数，避免报错
        currentPage.onPageScroll = () => { };
      }
    },

    // 计算组件高度
    calculateHeight() {
      const query = this.createSelectorQuery();
      query.select('.floating-header').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          const height = res[0].height;

          // 更新属性
          this.setData({
            placeholderHeight: height,
            'expandedHeight': height
          });
        }
      });
    },

    // 更新占位元素高度
    updatePlaceholderHeight() {
      const height = this.data.isCollapsed ?
        this.properties.collapsedHeight :
        this.properties.expandedHeight;

      this.setData({
        placeholderHeight: height
      });
    },

    // 手动设置折叠状态
    setCollapsed(collapsed) {
      this.setData({ isCollapsed: !!collapsed }, () => {
        this.updatePlaceholderHeight();
        this.forceUpdateSecondaryContent();
      });
    },

    // 强制更新次要内容区域
    forceUpdateSecondaryContent() {
      // 获取次要内容区域
      const query = this.createSelectorQuery();
      query.select('.secondary-content').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          // 如果处于折叠状态，强制隐藏次要内容
          if (this.data.isCollapsed) {
            // 使用样式强制隐藏
            res[0].node && (res[0].node.style.display = 'none');
            res[0].node && (res[0].node.style.height = '0');
            res[0].node && (res[0].node.style.overflow = 'hidden');
            res[0].node && (res[0].node.style.opacity = '0');
          } else {
            // 恢复显示
            res[0].node && (res[0].node.style.display = 'block');
            res[0].node && (res[0].node.style.height = 'auto');
            res[0].node && (res[0].node.style.overflow = 'visible');
            res[0].node && (res[0].node.style.opacity = '1');
          }
        }
      });
    }
  }
})
