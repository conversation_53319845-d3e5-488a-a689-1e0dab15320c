.login-overlay-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.login-overlay-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
  text-align: center;
}

.login-overlay-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 24rpx;
  text-align: center;
}

/* 登录错误消息样式 */
.login-error-message {
  display: flex;
  align-items: center;
  background-color: #fff2f0;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.login-error-message t-icon {
  color: #e34d59;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.login-error-message text {
  font-size: 24rpx;
  color: #e34d59;
  line-height: 1.4;
}

/* 普通登录样式 */
.login-normal {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-button {
  width: 100% !important;
  margin-bottom: 24rpx;
}

.login-mock-container {
  width: 100%;
  margin-top: 16rpx;
}

.login-mock-button {
  width: 100% !important;
}

/* 隐私协议弹窗样式 */
.privacy-popup {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.privacy-popup-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  color: #333;
  text-align: center;
}

.privacy-popup-content {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.6;
  text-align: justify;
  padding: 0 8rpx;
}

.privacy-link {
  color: #0052D9;
  display: inline;
  font-weight: 500;
}

.privacy-popup-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 8rpx;
}

.privacy-disagree-btn,
.privacy-agree-btn {
  flex: 1;
  height: 88rpx !important;
}

.privacy-disagree-btn {
  margin-right: 20rpx;
  border: 2rpx solid #ddd !important;
}

.privacy-agree-btn {
  margin-left: 20rpx;
}