<view class="order-detail-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="订单详情" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{true}}" collapse-threshold="{{100}}">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="countdown-section">
      <countdown-timer
        id="countdown-timer"
        end-time="{{orderInfo.reservationEndTime}}"
        format="hms"
        timeout-text="已超时"
        custom-class="{{countdownWarningClass}}"
        bind:change="onCountdownChange"
        bind:timeout="onCountdownTimeout"
        bind:warning="onCountdownWarning"
      />
    </view>

    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary">
      <!-- 共享时段区域 -->
      <view class="shared-time-section">
        <view class="shared-time-tag">
          <t-icon name="time" size="32rpx" color="#ffffff" />
          <text>共享时间：{{lockRuleInfo.sharedTime || '暂无共享时间'}}</text>
        </view>
      </view>
    </view>
  </floating-header>

  <!-- 可滚动的内容区域 -->
  <view class="order-detail-content">
    <!-- 车位信息卡片 -->
    <view class="card">
      <view class="card-title-container">
        <view class="card-title">车位信息</view>
        <view class="navigation-btn" bindtap="openNavigation">
          <t-icon name="location" size="32rpx" color="#0052d9" />
          <text>导航到这里</text>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">地址：</view>
        <view class="info-value">
          <text>{{orderInfo.address || lockRuleInfo.address}}</text>
        </view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.shareStatus}}">
        <view class="info-label">共享状态：</view>
        <view class="info-value">
          <view class="status-tag">{{lockRuleInfo.shareStatus}}</view>
        </view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.location}}">
        <view class="info-label">车位区域：</view>
        <view class="info-value">{{lockRuleInfo.location}}</view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.spotNumber}}">
        <view class="info-label">车位号：</view>
        <view class="info-value">{{lockRuleInfo.spotNumber}}</view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.price}}">
        <view class="info-label">车位单价：</view>
        <view class="info-value">¥{{lockRuleInfo.price}}/小时</view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.capAmount}}">
        <view class="info-label">封顶价格：</view>
        <view class="info-value">¥{{lockRuleInfo.capAmount}}/天</view>
      </view>
      <view class="info-item" wx:if="{{lockRuleInfo.sharedTime}}">
        <view class="info-label">共享时段：</view>
        <view class="info-value">{{lockRuleInfo.sharedTime}}</view>
      </view>
      <view class="info-item" wx:if="{{orderInfo.plateNo}}">
        <view class="info-label">车牌号：</view>
        <view class="info-value plate-no">{{orderInfo.plateNo}}</view>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <view class="info-label">下单时间</view>
        <view class="info-value">
          <text class="date">{{orderInfo.orderDate}}</text>
          <text class="time">{{orderInfo.orderTime}}</text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">入场时间</view>
        <view class="info-value">
          <text class="date">{{orderInfo.enterDate}}</text>
          <text class="time">{{orderInfo.enterTime}}</text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">关锁时间</view>
        <view class="info-value">
          <text class="date">{{orderInfo.lockDownDate}}</text>
          <text class="time">{{orderInfo.lockDownTime}}</text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">开锁时间</view>
        <view class="info-value">
          <text class="date">{{orderInfo.lockUpDate}}</text>
          <text class="time">{{orderInfo.lockUpTime}}</text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">预计停放</view>
        <view class="info-value">
          <text>{{orderInfo.estimatedDuration}}（预计{{orderInfo.estimatedFee}}元）</text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="info-item">
        <view class="info-label">出场时间</view>
        <view class="info-value">
          <text class="date">{{orderInfo.exitDate}}</text>
          <text class="time">{{orderInfo.exitTime}}</text>
        </view>
      </view>
    </view>

    <!-- 温馨提示区域 -->
    <view class="card notice-card">
      <view class="card-title">温馨提示</view>
      <view class="notice-content">
        <view class="notice-item">• 如车辆停放超出共享时间，影响业主使用，超出时间按4倍收费</view>
        <view class="notice-item">• 下单后即开始计算，出场后停止收费，多余保证金原路退回</view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <!-- 主要操作按钮 -->
    <t-button
      theme="{{buttonCooldown.isActive ? 'light' : 'primary'}}"
      size="large"
      class="main-action-button"
      bindtap="onLockToggle"
      disabled="{{buttonCooldown.isActive}}"
      icon="{{orderInfo.lockStatus === 'down' ? 'lock-off' : 'lock-on'}}"
    >{{buttonCooldown.isActive ? buttonCooldown.remainingTime + 's' : (orderInfo.lockStatus === 'down' ? '开锁' : '关锁')}}</t-button>

    <!-- 取消链接（下方） -->
    <view
      class="cancel-link"
      bindtap="onCancelOrder"
      hover-class="cancel-link-hover"
    >
      <text>取消订单</text>
    </view>
  </view>

  <!-- 取消订单确认弹窗 -->
  <t-dialog
    visible="{{showCancelDialog}}"
    title="确定要取消此订单吗？"
    content="注意：&#10;1. 取消订单后，车位将重新开放给其他用户预订&#10;2. 如车辆已进场，则不可取消订单&#10;3. 频繁取消订单将影响您的信用记录&#10;4. 取消成功后，保证金将在1-3个工作日内退回"
    confirm-btn="{{ { content: '确定取消', variant: 'danger' } }}"
    cancel-btn="{{ { content: '继续使用' } }}"
    bind:confirm="onConfirmCancel"
    bind:cancel="onCancelDialog"
  />

  <!-- 操作失败提示弹窗 -->
  <t-dialog
    visible="{{showFailDialog}}"
    title="操作失败"
    content="{{failMessage}}"
    confirm-btn="我知道了"
    bind:confirm="onCloseFailDialog"
  />

  <!-- 升降锁操作加载弹窗 -->
  <t-popup
    visible="{{lockOperationPopup.visible}}"
    placement="center"
    class="lock-operation-popup"
    overlay-props="{{lockOperationPopup.overlayProps}}"
    close-on-overlay-click="{{false}}"
  >
    <view class="lock-operation-content">
      <!-- 状态指示器 -->
      <view class="lock-operation-status">
        <view wx:if="{{lockOperationPopup.loading}}" class="status-loading">
          <t-loading
            theme="dots"
            class="lock-operation-loading"
            loading="{{true}}"
          >
          </t-loading>
        </view>
        <view wx:elif="{{lockOperationPopup.showRetry}}" class="status-warning">
          <t-icon name="error-circle" size="86rpx" color="#FA9D3B" />
        </view>
      </view>

      <!-- 操作文本 -->
      <view class="lock-operation-text">{{lockOperationPopup.text}}</view>

      <!-- 操作按钮区域 -->
      <view wx:if="{{lockOperationPopup.showRetry}}" class="lock-operation-actions">
        <t-button
          theme="light"
          size="medium"
          class="cancel-button"
          bindtap="onCancelLockOperation"
        >取消</t-button>
        <t-button
          theme="{{buttonCooldown.isActive ? 'light' : 'primary'}}"
          size="medium"
          class="retry-button"
          bindtap="onRetryLockOperation"
          disabled="{{buttonCooldown.isActive}}"
        >{{buttonCooldown.isActive ? buttonCooldown.remainingTime + 's' : '重试'}}</t-button>
      </view>
    </view>
  </t-popup>

  <t-message id="t-message" />
</view>
