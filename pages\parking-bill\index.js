import Message from 'tdesign-miniprogram/message/index';
import { orderApi } from '~/api/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单总数
    totalOrders: 0,
    // 已完成订单数
    completedOrders: 0,
    // 待处理订单数
    pendingOrders: 0,
    // 当前激活的标签页
    activeTab: 'all',
    // 所有账单列表（未经筛选）
    allBillList: [],
    // 当前显示的账单列表（经过筛选）
    billList: [
      // {
      //   id: '1',
      //   carNumber: '浙A1D3S9',
      //   status: 'ongoing',
      //   statusText: '进行中',
      //   parkingName: '浙江省杭州市拱墅区温州路71号',
      //   spotNumber: '负1-B1-001',
      //   startTime: '2025-04-05 12:24',
      //   endTime: '2025-04-05 13:55',
      //   duration: '',
      //   fee: '',
      //   extraFee: ''
      // },
      // {
      //   id: '2',
      //   carNumber: '浙A1D3S9',
      //   status: 'completed',
      //   statusText: '已完成',
      //   parkingName: '浙江省杭州市拱墅区温州路71号',
      //   spotNumber: '负1-B1-001',
      //   startTime: '2025-04-05 12:24',
      //   endTime: '2025-04-05 13:55',
      //   duration: '2小时55分',
      //   fee: '6.00',
      //   extraFee: ''
      // },
      // {
      //   id: '3',
      //   carNumber: '浙A1D3S9',
      //   status: 'canceled',
      //   statusText: '已取消',
      //   parkingName: '浙江省杭州市拱墅区温州路71号',
      //   spotNumber: '负1-B1-001',
      //   startTime: '2025-04-05 12:24',
      //   endTime: '2025-04-05 13:55',
      //   duration: '0小时0分',
      //   fee: '0.00',
      //   extraFee: ''
      // },
      // {
      //   id: '4',
      //   carNumber: '浙A1D3S9',
      //   status: 'pending',
      //   statusText: '待补缴',
      //   parkingName: '浙江省杭州市拱墅区温州路71号',
      //   spotNumber: '负1-B1-001',
      //   startTime: '2025-04-05 12:24',
      //   endTime: '2025-04-05 13:55',
      //   duration: '0小时0分',
      //   fee: '120.00',
      //   extraFee: '21.00'
      // }
    ],
    // 是否正在加载
    loading: false,
    // 是否正在刷新
    refreshing: false,
    // 是否有更多数据
    hasMoreData: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 加载配置
    loadingProps: {
      size: '50rpx',
    },
    // 页面滚动位置
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取账单数据
    this.fetchBillData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时，确保浮动头部状态正确
    setTimeout(() => {
      this.resetFloatingHeader();
    }, 300);
  },

  /**
   * 重置浮动头部状态
   */
  resetFloatingHeader() {
    const query = wx.createSelectorQuery();
    query.select('#floatingHeader').node();
    query.exec((res) => {
      if (res && res[0] && res[0].node) {
        const headerComponent = res[0].node.component;
        if (headerComponent && typeof headerComponent.setCollapsed === 'function') {
          // 先设置为展开状态
          headerComponent.setCollapsed(false);

          // 如果页面已经滚动，则根据滚动位置设置折叠状态
          if (this.data.scrollTop > 80) {
            headerComponent.setCollapsed(true);
          }
        }
      }
    });
  },

  /**
   * 获取账单数据
   */
  async fetchBillData(isRefresh = false) {
    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({
        currentPage: 1,
        hasMoreData: true
      });
    }

    // 如果没有更多数据，直接返回
    if (!this.data.hasMoreData && !isRefresh) {
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true
    });

    try {
      // 构建查询参数
      const params = {
        current: this.data.currentPage,
        size: this.data.pageSize,
        model: {}
      };

      // 调用API获取订单列表
      const orderListRes = await orderApi.apiGetOrderList(params);

      // 处理API返回的数据
      if (orderListRes && orderListRes.code === 0 && orderListRes.data) {
        const { data } = orderListRes;

        // 处理订单列表数据
        const processedList = this.processOrderList(data.results || []);

        // 计算订单统计数据
        const stats = this.calculateOrderStats(data.results || []);

        // 更新数据
        this.setData({
          totalOrders: data.total || 0,
          completedOrders: stats.completedOrders,
          pendingOrders: stats.pendingOrders,
          allBillList: isRefresh ? processedList : [...this.data.allBillList || [], ...processedList],
          hasMoreData: data.curPage < data.pages,
          currentPage: this.data.currentPage + 1,
          loading: false,
          refreshing: false
        });

        // 根据当前标签页筛选数据
        this.filterBillListByTab();
      } else {
        // 处理错误情况
        this.showMessage(orderListRes?.msg || '获取账单数据失败', 'error');
        this.setData({
          loading: false,
          refreshing: false
        });
      }
    } catch (error) {
      console.error('获取账单数据失败:', error);
      this.showMessage('获取账单数据失败，请重试', 'error');
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 根据当前标签页筛选账单列表
   */
  filterBillListByTab() {
    const { activeTab, allBillList } = this.data;

    if (!allBillList || allBillList.length === 0) {
      this.setData({ billList: [] });
      return;
    }

    let filteredList = [];

    if (activeTab === 'all') {
      // 全部账单
      filteredList = allBillList;
    } else {
      // 根据状态筛选
      filteredList = allBillList.filter(item => item.status === activeTab);
    }

    this.setData({ billList: filteredList });
  },

  /**
   * 处理订单列表数据
   */
  processOrderList(orderList) {
    return orderList.map(order => {
      // 格式化日期和时间
      const formatDateTime = (dateTimeStr) => {
        return dateTimeStr || '';
      };

      // 计算停车时长
      const calculateDuration = (duration) => {
        if (!duration) return '';

        // 将字符串转换为数字（秒）
        const totalSeconds = parseInt(duration, 10);
        if (isNaN(totalSeconds) || totalSeconds < 0) return '0小时0分';

        // 计算小时和分钟
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);

        return `${hours}小时${minutes}分`;
      };

      // 映射状态
      const mapStatus = (statusObj) => {
        if (!statusObj || !statusObj.code) return { status: 'unknown', statusText: '未知' };

        const statusMap = {
          'PROCESSING': { status: 'ongoing', statusText: '进行中' },
          'PAID': { status: 'completed', statusText: '已完成' },
          'CANCELLED': { status: 'canceled', statusText: '已取消' },
          'PARTIALLY_PAID': { status: 'pending', statusText: '待补缴' }
        };

        return statusMap[statusObj.code] || { status: 'unknown', statusText: statusObj.desc || '未知' };
      };

      // 获取状态信息
      const statusInfo = mapStatus(order.status);

      // 构建账单项
      // 对于进行中的订单，如果没有结束时间，显示为"现在"
      const endTime = statusInfo.status === 'ongoing' && !order.paySuccessDatetime
        ? '现在'
        : formatDateTime(order.paySuccessDatetime || '');

      return {
        id: order.id,
        lockId: order.lockId || '', // 添加锁ID
        carNumber: order.plateNo || '',
        status: statusInfo.status,
        statusText: statusInfo.statusText,
        parkingName: order.parkName || '',
        spotNumber: `${order.location || ''} ${order.code || ''}`,
        startTime: formatDateTime(order.orderTime),
        endTime: endTime,
        duration: calculateDuration(order.duration),
        fee: order.parkAmount || '0.00',
        extraFee: order.unpaidAmount || '0.00'
      };
    });
  },

  /**
   * 计算订单统计数据
   */
  calculateOrderStats(orderList) {
    let completedOrders = 0;
    let pendingOrders = 0;

    orderList.forEach(order => {
      if (order.status && order.status.code) {
        if (order.status.code === 'CLOSED') {
          completedOrders++;
        } else if (order.status.code === 'PARTIALLY_PAID') {
          pendingOrders++;
        }
      }
    });

    return {
      completedOrders,
      pendingOrders
    };
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const { value } = e.detail;

    // 如果切换到相同标签，不做处理
    if (value === this.data.activeTab) return;

    // 更新当前标签页
    this.setData({
      activeTab: value
    }, () => {
      // 根据当前标签页筛选数据
      this.filterBillListByTab();
    });
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.fetchBillData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.loading) {
      this.fetchBillData();
    }
  },

  /**
   * 页面滚动事件的处理函数
   */
  onPageScroll(e) {
    // 保存滚动位置
    this.setData({ scrollTop: e.scrollTop });
  },

  /**
   * 点击账单项
   */
  onItemTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/parking-bill-detail/index?id=${id}`
    });
  },


  /**
   * 查看订单详情
   */
  onViewDetail(e) {
    const { id, lockId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/parking-bill-detail/index?orderId=${id}&&lockId=${lockId}`
    });
  },

  /**
   * 解锁停车
   */
  onUnlockParking(e) {
    const { id, lockId } = e.currentTarget.dataset;
    // 跳转到订单详情页面，并传递锁ID参数
    wx.navigateTo({
      url: `/pages/order-detail/index?id=${id}&lockId=${lockId}`
    });
  },

  /**
   * 支付补缴
   */
  async onPayExtra(e) {
    const { id } = e.currentTarget.dataset;

    // 显示加载状态
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    try {
      // 这里应该调用支付API，目前保留模拟实现
      // 实际项目中应该使用类似 walletApi.apiPayDeposit 的API

      // 模拟支付流程
      setTimeout(() => {
        wx.hideLoading();

        // 支付成功
        this.showMessage('支付成功', 'success');

        // 刷新数据
        this.fetchBillData(true);
      }, 1500);

      // 实际实现可能类似：
      /*
      const payResult = await orderApi.apiPayOrder({
        id: id,
        payMethod: 'WXPAY_LITE'
      });

      wx.hideLoading();

      if (payResult && payResult.code === 0 && payResult.data) {
        // 调起微信支付
        wx.requestPayment({
          timeStamp: payResult.data.timeStamp,
          nonceStr: payResult.data.nonceStr,
          package: payResult.data.package,
          signType: payResult.data.signType || 'RSA',
          paySign: payResult.data.paySign,
          success: () => {
            this.showMessage('支付成功', 'success');
            this.fetchBillData(true);
          },
          fail: (err) => {
            if (err.errMsg.indexOf('cancel') > -1) {
              this.showMessage('支付已取消', 'warning');
            } else {
              this.showMessage('支付失败，请重试', 'error');
            }
          }
        });
      } else {
        this.showMessage(payResult?.msg || '获取支付信息失败', 'error');
      }
      */
    } catch (error) {
      wx.hideLoading();
      console.error('支付过程中出现错误:', error);
      this.showMessage('支付过程中出现错误，请重试', 'error');
    }
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
