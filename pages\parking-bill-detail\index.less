@import '/variable.less';

/* 主容器样式 */
.bill-detail-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  padding-bottom: 120rpx; /* 增加底部空间，为底部操作栏留出位置 */
  box-sizing: border-box;
  position: relative;
}

/* 内容区域样式 */
.bill-detail-content {
  padding: 30rpx 24rpx 40rpx;
  position: relative;
}

/* 浮动头部样式 */
.bill-info-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // padding: 30rpx 0;
  position: relative;
  z-index: 2;
}

.bill-status-text {
  font-size: 42rpx;
  font-weight: 500;
  color: #FFFFFF;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.bill-amount {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.amount-label {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 10rpx;
  font-weight: 400;
}

.amount-value {
  font-size: 48rpx;
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.bill-unpaid {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4rpx;
  opacity: 0.9;
}

.unpaid-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 8rpx;
  font-weight: 400;
}

.unpaid-value {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.bill-info-secondary {
  margin-top: 24rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bill-info-row {
  display: flex;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 0;
  backdrop-filter: blur(10rpx);
  margin: 0 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: calc(100% - 48rpx);
}

.bill-info-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 16rpx;
  align-items: center;
  text-align: center;
  justify-content: center;
  height: 90rpx;
  flex: 1;
}

.info-label {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36rpx;
  margin-bottom: 12rpx;

  .t-icon {
    margin-right: 8rpx;
    padding: 4rpx;
  }

  .label-text {
    font-size: 24rpx;
    font-weight: 400;
    letter-spacing: 2rpx;
    color: rgba(255, 255, 255, 0.9);
  }
}

.info-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 35rpx;
  height: 35rpx;
  width: 100%;
  overflow: hidden;
}

.value-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.info-divider {
  width: 2rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 10rpx 0;
  align-self: stretch;
  height: 80%;
}

.unpaid-notice {
  margin-top: 16rpx;
  background-color: rgba(250, 81, 81, 0.8);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 8rpx 24rpx;
  border-radius: 22.5rpx;
  font-weight: 500;
}

/* 卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 30rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 28rpx;
    background-color: @brand7-normal;
    border-radius: 3rpx;
  }
}

/* 信息项样式 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: @gy2;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: @gy1;
  text-align: right;
  flex: 1;
  &.order-no {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .copy-btn {
      margin-left: 12rpx;
      color: @brand7-normal;
      font-size: 24rpx;
      padding: 4rpx 12rpx;
      border: 1rpx solid @brand7-normal;
      border-radius: 20rpx;
      line-height: 1;
    }
  }
  .date {
    margin-right: 12rpx;
  }

  .time {
    color: @gy2;
  }

  .status {
    color: #FA9D3B;
  }

  .plate-no {
    font-weight: 500;
    color: @gy1;
    letter-spacing: 1rpx;
  }

  /* 移除了address-container样式 */

  .status-tag {
    display: inline-block;
    font-size: 22rpx;
    color: #fff;
    background-color: @brand7-normal;
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
  }
}

/* 分隔线 */
.divider {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 6rpx 0;
  opacity: 0.8;
}


/* 状态样式 */
.status-normal {
  color: #0052D9;
}

.status-warning {
  color: #FA9D3B;
}
.status-large {
  color: @brand7-normal;
  font-size: 20px;
  font-weight: 800;
}
/* 分隔线 */
.divider {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 16rpx 0;
}

/* 可折叠区域 */
.collapsible-section {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: 1000rpx; /* 足够大的高度以容纳内容 */
  }
}

/* 展开详情按钮 */
.expand-details {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  color: #959595;
  font-size: 26rpx;
  padding: 12rpx 0;

  t-icon {
    margin-left: 8rpx;
  }
}

/* 状态样式 */
.status-normal {
  color: #959595;
}

.status-warning {
  color: #FA9D3B;
}

.status-error {
  color: #FA5151;
}

.status-success {
  color: #07C160;
}

/* 退款信息 */
.refund-info {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #959595;
  padding: 16rpx 0;

  t-icon {
    margin-right: 8rpx;
  }
}

/* 申请退款按钮 */
.refund-button-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.refund-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 48rpx;
  border: 2rpx solid @brand7-normal;
  border-radius: 44rpx;
  color: @brand7-normal;
  font-size: 28rpx;
  font-weight: 500;

  &:active {
    opacity: 0.8;
    transform: translateY(2rpx);
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 16rpx 40rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 95;
  border-top: 1rpx solid #f0f0f0;
}

.order-button {
  height: 88rpx !important;
  font-size: 32rpx !important;
  border-radius: 44rpx !important;
  font-weight: 500 !important;
  letter-spacing: 2rpx !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.15) !important;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 4rpx rgba(0, 82, 217, 0.15) !important;
  }
}

/* 装饰性元素 */
floating-header {
  position: relative;
  overflow: hidden;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 300rpx;
    height: 300rpx;
    border-radius: 50%;
    background-color: rgba(208, 220, 248, 0.17);
    z-index: 1;
  }

  &::before {
    top: -150rpx;
    right: -100rpx;
  }

  &::after {
    bottom: -150rpx;
    left: -100rpx;
  }
}
