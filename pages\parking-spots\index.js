import Message from 'tdesign-miniprogram/message';
import { splitTimeStr, openNavigation, sortArrByContent } from '~/utils/util';
import { parkApi, lockApi, request } from '~/api/index';


Page({
  data: {
    enable: false,
    loading: true,
    parkingId: '',
    parkingInfo: {
      name: '南北商务港',
      address: '浙江省杭州市拱墅区温州路71号',
      distance: '633m',
      price: '3',
      availableSpots: 43,
      image: '',
      latitude: 30.2741, // 示例经纬度，实际项目中应从API获取
      longitude: 120.1551 // 示例经纬度，实际项目中应从API获取
    },
    parkingSpots: [],
    refreshTimestamp: 0, // 用于记录最后刷新时间
    current: 1, // 当前页码，从1开始
    hasMore: true, // 是否有更多数据
    loadMoreStatus: 0, // 加载更多状态：0-默认，1-加载中，2-已全部加载，3-加载失败
    scrollTop: 0 // 记录滚动位置
  },

  onLoad(options) {
    const { id } = options;

    if (id) {
      this.setData({
        parkingId: id,
        loading: true
      }, async () => {
        try {
          // 先获取停车场信息
          await this.fetchParkingInfo();
          // 然后获取车位列表
          await this.fetchParkingSpots();
        } catch (error) {
          console.error('初始化数据失败:', error);
          this.setData({ loading: false });
        }
      });
    } else {
      this.showMessage('停车场ID不存在', 'error');
      setTimeout(() => {
        wx.navigateBack({
          fail: () => {
            wx.switchTab({
              url: '/pages/home-park/index'
            });
          }
        });
      }, 1500);
    }

    // 获取浮动头部组件实例
    this.floatingHeader = this.selectComponent('#floatingHeader');
  },

  onReady() {
    // 页面初次渲染完成
  },

  onShow() {
    // 页面显示
  },

  // 返回按钮点击事件
  onBackTap() {
    wx.navigateBack({
      url: '/pages/home-park/index'
    });
  },

  onRefresh() {
    this.refresh();
  },

  // 刷新数据
  refresh() {
    // 防止频繁刷新，设置最小刷新间隔为2秒
    const now = Date.now();
    if (now - this.data.refreshTimestamp < 2000) {
      this.setData({ enable: false });
      return Promise.resolve();
    }

    // 检查是否有停车场ID
    if (!this.data.parkingId) {
      this.showMessage('停车场ID不存在，无法刷新', 'error');
      this.setData({ enable: false });
      return Promise.resolve();
    }

    // 重置页码和状态
    this.setData({
      enable: true,
      refreshTimestamp: now,
      current: 1, // 重置为第1页
      hasMore: true,
      loadMoreStatus: 0, // 重置加载更多状态
      scrollTop: 0 // 刷新时回到顶部
    });

    // 先获取停车场信息，再获取车位列表
    return this.fetchParkingInfo()
      .then(() => this.fetchParkingSpots())
      .then(() => {
        wx.nextTick(() => {
          this.showMessage('刷新成功', 'success');
        });
      })
      .catch(error => {
        console.error('刷新失败:', error);
        wx.nextTick(() => {
          this.showMessage('刷新失败，请稍后再试', 'error');
        });
      })
      .finally(() => {
        setTimeout(() => {
          this.setData({ enable: false });
        }, 800);
      });
  },

  // 加载更多数据
  loadMore() {
    if (!this.data.hasMore || this.data.loading || this.data.loadMoreStatus === 1) return;

    // 增加页码
    const nextPage = this.data.current + 1;

    // 只更新loadMoreStatus状态，不触发整个页面的loading状态
    this.setData({
      current: nextPage,
      loadMoreStatus: 1 // 设置为加载中状态
    }, () => {
      // 在页码更新后加载下一页数据
      this.fetchParkingSpots().then(() => {
        // 静默加载，不显示成功消息
      }).catch(() => {
        // 如果加载失败，恢复之前的页码
        wx.nextTick(() => {
          this.setData({
            current: this.data.current - 1,
            loadMoreStatus: 3 // 设置为加载失败状态
          });
          this.showMessage('加载更多失败', 'error');
        });
      });
    });
  },

  // 页面滚动事件处理函数
  onPageScroll(e) {
    // 记录滚动位置
    this.setData({
      scrollTop: e.scrollTop
    });
  },

  // 页面滚动到底部时触发
  onReachBottom() {
    // 防止重复触发，增加节流处理
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
    }

    this._reachBottomTimer = setTimeout(() => {
      if (this.data.hasMore && !this.data.loading && this.data.loadMoreStatus !== 1) {
        this.loadMore();
      }
    }, 200); // 200ms的节流时间
  },

  // 重试加载
  onRetryLoad() {
    if (this.data.loadMoreStatus === 3) {
      this.loadMore();
    }
  },

  // 页面卸载时清理资源
  onUnload() {
    // 清理定时器
    if (this._reachBottomTimer) {
      clearTimeout(this._reachBottomTimer);
      this._reachBottomTimer = null;
    }
  },

  // 获取停车场信息
  async fetchParkingInfo() {
    if (!this.data.parkingId) {
      console.error('停车场ID不存在');
      this.showMessage('停车场ID不存在', 'error');
      return Promise.reject(new Error('停车场ID不存在'));
    }

    try {
      // 使用 apiGetParkDetail 接口获取停车场信息
      const result = await parkApi.apiGetParkDetail({
        id: this.data.parkingId
      });

      if (result && result.code === 0) {
        if (result.data) {
          // 如果API返回成功且data不为null，使用API返回的数据
          this.setData({
            parkingInfo: {
              id: this.data.parkingId,
              name: result.data.parkName || '停车场',
              address: result.data.parkAddress || '地址信息未提供',
              distance: this.formatDistance(result.data.distance || '0'),
              price: result.data.unitCharge || '3',
              availableSpots: result.data.availableSlots || 0,
              image: result.data.image || '',
              parkType: result.data.parkType ? result.data.parkType.desc : '普通停车场',
              timeUnit: result.data.timeUnit ? result.data.timeUnit.desc : '小时',
              averagePrice: result.data.averagePrice || '',
              latitude: result.data.latitude || '',
              longitude: result.data.longitude || '',
            }
          });
        } else {
          // 如果API返回成功但data为null，使用默认数据
          this.setData({
            parkingInfo: {
              id: this.data.parkingId,
              name: '停车场',
              address: '地址信息未提供',
              distance: '未知',
              price: '3',
              availableSpots: 0,
              image: '',
              parkType: '普通停车场',
              timeUnit: '小时',
              averagePrice: ''
            }
          });
        }
        return Promise.resolve();
      } else {
        throw new Error(result.errorMsg || '获取停车场信息失败');
      }
    } catch (error) {
      console.error('获取停车场信息失败:', error);
      this.showMessage('获取停车场信息失败', 'error');
      return Promise.reject(error);
    }
  },

  // 格式化距离显示
  formatDistance(distance) {
    if (!distance) return '未知';

    // 将米转换为千米，并保留一位小数
    const distanceNum = parseFloat(distance);
    if (isNaN(distanceNum)) return distance;

    if (distanceNum < 1000) {
      return `${Math.round(distanceNum)}m`;
    } else {
      return `${(distanceNum / 1000).toFixed(1)}km`;
    }
  },

  // 获取车位列表
  fetchParkingSpots() {
    if (!this.data.parkingId) {
      console.error('停车场ID不存在');
      this.showMessage('停车场ID不存在', 'error');
      return Promise.reject(new Error('停车场ID不存在'));
    }

    // 分离加载状态：初始加载使用loading，加载更多只使用loadMoreStatus
    this.setData({
      loading: this.data.current === 1 ? true : false,
      loadMoreStatus: this.data.current > 1 ? 1 : 0 // 如果是加载更多，设置状态为1（加载中）
    });

    return new Promise((resolve, reject) => {
      // 使用apiGetParkLockList获取车位锁列表
      lockApi.apiGetParkLockList({
        current: this.data.current,
        size: 10, // 每页10条数据
        model: {
          "parkId": this.data.parkingId
        }
      })
        .then(res => {
          if (res && res.code === 0 && res.data) {
            // 检查是否有结果数据
            const results = res.data.results || [];

            // 转换API返回的数据为页面需要的格式
            const newParkingSpots = results.map(item => ({
              id: item.lockId || item.lotId || '',
              name: `车位 ${item.code || ''}`,
              code: item.code || '',
              location: item.location || '未知位置',
              shareStartTime: item.shareStartTime || '00:00',
              shareEndTime: item.shareEndTime || '00:00',
              shareTime: `${splitTimeStr(item.shareStartTime) || '00:00'} - ${(item.isCrossDay.code == 'YES') ? '次日' : ''}${splitTimeStr(item.shareEndTime) || '00:00'}`,
              price: item.price || this.data.parkingInfo.price || '3',
              // 保存原始状态对象
              isCrossDay: {
                code: item.isCrossDay ? item.isCrossDay.code : 'NO',
                desc: item.isCrossDay ? item.isCrossDay.desc : '否'
              },
              useStatus: {
                code: item.useStatus ? item.useStatus.code : 'NO',
                desc: item.useStatus ? item.useStatus.desc : '否'
              },
              ifAvailable: {
                code: item.ifAvailable ? item.ifAvailable.code : 'NO',
                desc: item.ifAvailable ? item.ifAvailable.desc : '否'
              },
              // 简化的状态标志，用于UI显示
              status: item.useStatus && item.useStatus.code === 'YES' ? 'busy' : 'free',
              isAvailable: item.ifAvailable && item.ifAvailable.code === 'YES'
            }));

            // 判断是否有更多数据
            const totalPages = parseInt(res.data.pages) || 0;
            const hasMore = this.data.current < totalPages;

            // 如果是第一页，直接替换数据，否则追加数据
            const parkingSpots = this.data.current === 1
              ? newParkingSpots
              : this.data.parkingSpots.concat(newParkingSpots);
            // 进行归类排序
            const parkingSpotsBysorted = sortArrByContent(parkingSpots, 'isAvailable')
            // 使用nextTick延迟状态更新，减少渲染次数
            wx.nextTick(() => {
              this.setData({
                parkingSpots: parkingSpotsBysorted,
                hasMore,
                loadMoreStatus: hasMore ? 0 : 2, // 0-默认，2-已全部加载
                loading: false
              });
              resolve();
            });
          }
        });
    });
  },

  // 点击车位
  onSpotTap(e) {
    const { id } = e.currentTarget.dataset;
    const spot = this.data.parkingSpots.find(item => item.id === id);

    if (!spot) {
      this.showMessage('车位信息不存在', 'error');
      return;
    }

    // 检查车位是否可用
    if (!spot.isAvailable) {
      this.showMessage('该车位当前不可用', 'warning');
      return;
    }

    // 检查车位是否被占用
    if (spot.status === 'busy') {
      this.showMessage('该车位正在使用中', 'warning');
      return;
    }
    // 导航到车位详情页
    wx.navigateTo({
      url: `/pages/parking-spots-detail/index?id=${id}`
    });
  },

  // 预约车位（实际项目中使用）
  async bookParkingSpot(spotId) {
    try {
      // 实际项目中应该使用
      // const res = await request('/parking/book', 'POST', {
      //   parkingId: this.data.parkingId,
      //   spotId
      // });

      // 模拟预约成功
      this.showMessage('预约成功', 'success');

      // 刷新车位列表
      setTimeout(() => {
        this.fetchParkingSpots();
      }, 1000);
    } catch (error) {
      console.error('预约失败:', error);
      this.showMessage('预约失败，请稍后再试', 'error');
    }
  },

  // 锁定车位
  onLockTap(e) {
    const { id, name } = e.currentTarget.dataset;

    // 阻止事件冒泡
    e.stopPropagation();

    // 这里可以添加锁定车位的逻辑
    console.log('锁定车位:', id, name);

    // 模拟锁定成功
    wx.showModal({
      title: '锁定车位',
      content: `确定要锁定 ${name} 吗？`,
      confirmText: '确定锁定',
      confirmColor: '#0052D9',
      success: (res) => {
        if (res.confirm) {
          // 模拟API请求
          wx.showLoading({
            title: '锁定中...',
            mask: true
          });

          setTimeout(() => {
            wx.hideLoading();

            // 更新车位状态
            const parkingSpots = this.data.parkingSpots.map(item => {
              if (item.id === id) {
                return {
                  ...item,
                  status: 'busy'
                };
              }
              return item;
            });

            this.setData({ parkingSpots });
            this.showMessage('锁定成功', 'success');
          }, 1000);
        }
      }
    });
  },

  // 解锁车位
  onUnlockTap(e) {
    const { id, name } = e.currentTarget.dataset;

    // 阻止事件冒泡
    e.stopPropagation();

    // 这里可以添加解锁车位的逻辑
    console.log('解锁车位:', id, name);

    // 模拟解锁成功
    wx.showModal({
      title: '解锁车位',
      content: `确定要解锁 ${name} 吗？`,
      confirmText: '确定解锁',
      confirmColor: '#0052D9',
      success: (res) => {
        if (res.confirm) {
          // 模拟API请求
          wx.showLoading({
            title: '解锁中...',
            mask: true
          });

          setTimeout(() => {
            wx.hideLoading();

            // 更新车位状态
            const parkingSpots = this.data.parkingSpots.map(item => {
              if (item.id === id) {
                return {
                  ...item,
                  status: 'free'
                };
              }
              return item;
            });

            this.setData({ parkingSpots });
            this.showMessage('解锁成功', 'success');
          }, 1000);
        }
      }
    });
  },

  // 打开导航
  onNavigationTap() {
    const { parkingInfo } = this.data;

    if (!parkingInfo.latitude || !parkingInfo.longitude) {
      this.showMessage('位置信息不完整，无法导航', 'warning');
      return;
    }

    openNavigation({
      latitude: parkingInfo.latitude,
      longitude: parkingInfo.longitude,
      name: parkingInfo.name,
      address: parkingInfo.address
    }).catch(err => {
      console.error('导航失败:', err);
      // 错误处理已在 openNavigation 函数中完成
    });
  },

  // 显示消息提示
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
