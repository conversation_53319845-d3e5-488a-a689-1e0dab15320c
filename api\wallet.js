import request from './request';

/* apiGenerateUniqueId 响应数据结构示例
  {
    "code": 0,
    "data": "1921429383403409408",
    "msg": "成功",
    "path": null,
    "timestamp": "1746939438119",
    "errorMsg": null
  }
*/
export const apiGenerateUniqueId = async () => {
  const uniqueIdRes = await request('/mini/park/deposit/unique/id/generate', 'POST');
  return uniqueIdRes.data;
};

/* apiPayDeposit 响应数据结构示例
  {
    "code": 0,
    "data": {
      "timeStamp": "1746952961",
      "signType": "HMAC-SHA256",
      "package": "prepay_id=wx11164235127501886f0159db4827d10001",
      "paySign": "46B49CF8AA876A793494EC94DE95338E4D255D33BE419207C665DCC6ECD76F25",
      "nonceStr": "1746952961792",
      "appId": "wxa5f7c5d4b5c1d034",
      "id": 1
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1746952963840",
    "errorMsg": null
  }
*/
export const apiPayDeposit = async (payload) => {
  const { uniqueId, payType, orderId, payMethod, payAmount } = payload;
  const depositPayRes = await request('/mini/park/deposit/pay', 'POST', {
    uniqueId,
    payType,
    orderId,
    payMethod,
    payAmount
  });
  return depositPayRes.data;
};

/* apiCancelPayment 响应数据结构示例
  {
    "code": 0,
    "data": null,
    "msg": "成功",
    "path": null,
    "timestamp": "1747594273554",
    "errorMsg": null
  }
*/
export const apiCancelPayment = async (payload) => {
  const { id } = payload;
  const cancelRes = await request('/mini/park/deposit/cancel', 'POST', {
    id
  });
  return cancelRes.data;
};

/* apiGetDepositAmount 响应数据结构示例
  {
    "code": 0,
    "data": {
      "depositAmount": "99.00",
      "depositStatus": {
        "code": "PAID",
        "desc": "已支付"
      }
    },
    "msg": "成功",
    "path": null,
    "timestamp": "*************",
    "errorMsg": null
  }
*/
export const apiGetDepositAmount = async () => {
  const depositRes = await request('/mini/account/getDepositAmount', 'POST');
  return depositRes.data;
};

/* apiGetWalletBalance 响应数据结构示例
  {
    "code": 0,
    "data": "199.00",                                 // 钱包余额
    "msg": "成功",
    "path": null,
    "timestamp": "*************",
    "errorMsg": null
  }
*/
export const apiGetWalletBalance = async () => {
  const balanceRes = await request('/mini/account/getTotalAmount', 'POST');
  return balanceRes.data;
};
