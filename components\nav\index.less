@import '/variable.less';

.custom-image {
  height: 24px;
  width: 87px;
}
.custom-navbar {
  --td-navbar-color: #fff;
  --td-navbar-bg-color: transparent;
  --td-navbar-left-arrow-color: #fff;
  background-color: transparent !important;
}
.gradient-navbar {
  .navbar-title {
    color: #fff !important;
    font-weight: 500;
  }
}
.search-box {
  display: flex;
  justify-content: center;
  width: 100%;
  .t-search {
    --td-search-height: 28px;
    --td-search-font-size: @font-size-default;
    width: 85%;
    --td-search-bg-color: #ffffff3a;
    .t-icon {
      font-size: @font-size-default !important;
    }
  }
}

.t-navbar-content {
  background-color: transparent !important;
  // background-color:rgba(248, 248, 248, 0.795);
  padding-bottom: 10px;
}

.t-navbar-placeholder {
  background-color: transparent !important;
}

page {
  --td-search-font-size: 18px;
}