<!-- pages/plate-management/index.wxml -->
<t-message id="t-message" />

<view class="plate-management-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="车牌管理" background="{{true}}" fixed="{{true}}" bind:back="onBackTap" />



  <!-- 浮动头部 -->
  <floating-header enable-collapse="{{false}}">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main">
      <!-- 空内容，保持视觉一致性 -->
    </view>

    <!-- 次要内容（折叠时隐藏） -->
    <view slot="secondary">
      <!-- 可以根据需要添加内容 -->
    </view>
  </floating-header>

  <!-- 内容区域 -->
  <view class="plate-management-content">
    <!-- 车牌列表 -->
    <view class="plate-list">
      <!-- 加载状态 -->
      <block wx:if="{{loading && plateList.length === 0}}">
        <view class="loading-container">
          <t-loading theme="circular" size="40rpx" loading />
          <text class="loading-text">加载中...</text>
        </view>
      </block>

      <!-- 空状态 -->
      <block wx:elif="{{plateList.length === 0}}">
        <view class="empty-container">
          <!-- 添加按钮 -->
          <view  class="add-button-container">
            <t-button
              theme="primary"
              size="large"
              icon="add"
              bindtap="showAddDialog"
              block
            >
              添加新车牌
            </t-button>
          </view>
        </view>
      </block>

      <!-- 车牌列表 -->
      <block wx:else>
        <view
          class="plate-item"
          wx:for="{{plateList}}"
          wx:key="id"
        >
          <!-- 车牌卡片 -->
          <view class="plate-card">
            <!-- 车牌信息区域 -->
            <view class="plate-info">
              <view class="plate-header">
                <view class="plate-number-container">
                  <view class="plate-number">{{item.plateNo}}</view>
                  <view wx:if="{{item.isDefault}}" class="default-tag">默认</view>
                </view>
              </view>
              <view class="plate-meta">
                <text class="plate-time">添加时间：{{item.createdTime}}</text>
              </view>
            </view>

            <!-- 右侧操作区域 -->
            <view class="plate-actions">
              <view class="action-buttons">
                <t-button
                  wx:if="{{!item.isDefault}}"
                  theme="light"
                  size="small"
                  bindtap="setDefaultPlate"
                  data-plate="{{item}}"
                >
                  设为默认
                </t-button>
                <t-button
                  theme="light"
                  size="small"
                  icon="delete"
                  bindtap="deletePlate"
                  data-plate="{{item}}"
                >
                  删除
                </t-button>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 添加按钮 -->
    <view wx:if="{{plateList.length > 0}}" class="add-button-container">
      <t-button
        theme="primary"
        size="large"
        icon="add"
        bindtap="showAddDialog"
        block
      >
        添加新车牌
      </t-button>
    </view>
  </view>
</view>

<!-- 添加车牌弹窗 -->
<t-popup
  visible="{{showAddDialog}}"
  placement="bottom"
  bind:visible-change="closeAddDialog"
>
  <view class="add-plate-popup">
    <view class="add-plate-popup__header">
      <view class="add-plate-popup__title">添加车牌</view>
      <t-icon name="close" size="44rpx" color="#999999" bindtap="closeAddDialog" />
    </view>

    <view class="add-plate-popup__content">
      <t-input
        label="车牌号"
        placeholder="请输入车牌号"
        value="{{newPlate}}"
        bind:change="onPlateInput"
        maxcharacter="20"
        style="margin-bottom: 20rpx;"
      />

      <view class="plate-format-hint">
        <t-icon name="info-circle" size="32rpx" color="#999999" />
        <text>格式示例：浙A12345</text>
      </view>
    </view>

    <view class="add-plate-popup__footer">
      <t-button
        theme="light"
        size="large"
        bindtap="closeAddDialog"
        style="margin-right: 20rpx;"
      >
        取消
      </t-button>
      <t-button
        theme="primary"
        size="large"
        bindtap="addNewPlate"
        loading="{{loading}}"
      >
        确认添加
      </t-button>
    </view>
  </view>
</t-popup>
