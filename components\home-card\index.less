@import '/variable.less';

.home-card {
  position: relative;
  margin: 0; // 移除默认 margin，由父容器控制布局
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
  will-change: transform; /* 提示浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 启用GPU加速 */

  &:active {
    transform: scale(0.98);
  }
  // 添加微妙的边框效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1rpx solid rgba(126, 126, 126, 0.2);
    border-radius: 12rpx;
    z-index: 2;
    pointer-events: none;
  }

  &__image {
    display: block;
    width: 100%;
    height: 100%; // 根据需求调整高度
  }

  &__info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24rpx;
    // background-color: rgba(0, 0, 0, 0.3); // 添加半透明背景色，确保文字清晰可见
    // color: rgb(53, 53, 53);
    font-weight: 500;
    z-index: 1;
    text-align: right;
    color: #959595;
    font-size: small;
    padding-right: 18px;
    padding-bottom: 6px;
  }
}
