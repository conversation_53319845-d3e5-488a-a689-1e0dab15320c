import request from './request';

export const apiGetParkLockList = async (payload) => {
  const { current = 1, size = 10, model = {} } = payload;
  const lockListRes = await request('/device/lock/getParkLockList', 'POST', {
    current,
    size,
    model
  });
  return lockListRes.data;
};

/* apiGetLockRuleDetail 响应数据结构示例
  {
    "code": 0,
    "data": {
        "id": "1922845176542851074",                   // 共享方案id
        "parkId": "1922843368063823874",               // 车场id
        "lotId": "1922845176475742210",                // 车位id
        "lockId": "1922845176509296642",               // 车锁id
        "price": "10.00",                              // 车位价格
        "shareStartTime": "08:00:00",                  // 共享开始时间
        "shareEndTime": "20:00:00",                    // 共享结束时间
        "isCrossDay": {                                // 是否跨天
            "code": "NO",
            "desc": "否"
        },
        "shareStatus": null,                           // 共享状态
        "capAmount": "30.00",                          // 封顶价格
        "location": "地下一层",                         // 车位区域
        "code": "B101",                                // 车位编号
        "parkName": null,                              // 车场名称
        "parkAddress": "浙江省杭州市拱墅区停车场",       // 车场地址
        "useStatus": {                                 // 是否正在被使用
            "code": "NO",
            "desc": "否"
        }
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747571831833",
    "errorMsg": null
  }
*/
export const apiGetLockRuleDetail = async (payload) => {
  const { lockId } = payload;
  const ruleDetailRes = await request(`/device/lock/getRuleByLockId/${lockId}`, 'POST');
  return ruleDetailRes.data;
};

/* apiGetShareTimeOptions 响应数据结构示例
  {
    "code": 0,
    "data": [                                          // 可选择的共享时长（小时）
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10
    ],
    "msg": "成功",
    "path": null,
    "timestamp": "1747573334360",
    "errorMsg": null
  }
*/
export const apiGetShareTimeOptions = async (payload) => {
  const { ruleId } = payload;
  const timeOptionsRes = await request(`/mini/park/rule/time/${ruleId}`, 'POST');
  return timeOptionsRes.data;
};

/* apiGetLockCount 响应数据结构示例
  {
    "code": 0,
    "data": "2",                                       // 车锁数量
    "msg": "成功",
    "path": null,
    "timestamp": "1747591005158",
    "errorMsg": null
  }
*/
export const apiGetLockCount = async () => {
  const countRes = await request('/device/lock/countLocks', 'POST');
  return countRes.data;
};

/* apiGetPersonalLockList 响应数据结构示例
  {
    "code": 0,
    "data": {
        "curPage": "0",                                // 当前页码
        "pages": "1",                                  // 总页数
        "results": [                                   // 车锁列表
            {
                "id": "1922845373498978305",           // 共享方案id
                "parkId": "1922843368063823874",       // 车场id
                "lotId": "1922845373431869442",        // 车位id
                "lockId": "1922845373465423874",       // 车锁id
                "price": "10.00",                      // 车位单价
                "shareStartTime": "01:16:00",          // 共享开始时间
                "shareEndTime": "23:16:00",            // 共享结束时间
                "isCrossDay": {                        // 是否跨天
                    "code": "NO",
                    "desc": "否"
                },
                "shareStatus": {                       // 共享状态
                    "code": "YES",
                    "desc": "是"
                },
                "capAmount": "20.00",                  // 封顶价格
                "location": "地下一层",                 // 车位区域
                "code": "B102",                        // 车位编号
                "parkName": "杭州市拱墅区测试停车场",    // 车场名称
                "parkAddress": "浙江省杭州市拱墅区停车场", // 车场地址
                "useStatus": {                         // 是否正在被使用
                    "code": "NO",
                    "desc": "否"
                }
            },
            // 更多车锁...
        ],
        "size": "10",                                  // 每页记录数
        "total": "2",                                  // 总记录数
        "extraData": null,                             // 额外数据
        "cursor": null                                 // 游标
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747593623248",
    "errorMsg": null
  }
*/
export const apiGetPersonalLockList = async (payload) => {
  const { current = 1, size = 10, model = {} } = payload;
  const personalLockRes = await request('/device/lock/getRuleByCustomerId', 'POST', {
    current,
    size,
    model
  });
  return personalLockRes.data;
};

/* apiUpdateShareRule 响应数据结构示例
  {
    "code": 0,
    "data": null,
    "msg": "成功",
    "path": null,
    "timestamp": "1747594273554",
    "errorMsg": null
  }

  注意: 跨天时，共享开始时间要大于共享结束时间
*/
export const apiUpdateShareRule = async (payload) => {
  const {
    id,
    price,
    shareStartTime,
    shareEndTime,
    isCrossDay,
    shareStatus,
    cycleType,
    illegalMultiple,
    capAmount
  } = payload;
  const updateRes = await request('/mini/park/rule/update', 'POST', {
    id,
    price,
    shareStartTime,
    shareEndTime,
    isCrossDay,
    shareStatus,
    cycleType, //TODAY-今日、EVERYDAY-每日、WEEKEND-周末
    illegalMultiple,
    capAmount
  });
  return updateRes.data;
};

/* apiUpdateShareStatus 响应数据结构示例
  {
    "code": 0,
    "data": null,
    "msg": "成功",
    "path": null,
    "timestamp": "1747594273554",
    "errorMsg": null
  }
*/
export const apiUpdateShareStatus = async (payload) => {
  const { id, shareStatus } = payload;
  const statusRes = await request('/mini/park/rule/status', 'POST', {
    id,
    shareStatus
  });
  return statusRes.data;
};

/* apiCheckLockStatus 响应数据结构示例
  {
    "code": 0,
    "data": {
      "code": "CLOSE",                                  // OPEN/CLOSE
      "desc": "关闭(升锁)"                              // 开启/关闭
    },
    "msg": "成功",
    "path": null,
    "timestamp": "1747894587634",
    "errorMsg": null
  }
*/
export const apiCheckLockStatus = async (payload) => {
  const { lockId } = payload;
  const statusRes = await request(`/device/lock/checkLockStatus/${lockId}`, 'POST');
  return statusRes.data;
};

/* apiOperateLockWithUser 响应数据结构示例
  {
    "code": 0,
    "data": null,
    "msg": "成功",
    "path": null,
    "timestamp": "1747894587634",
    "errorMsg": null
  }

  注意: status参数应为字符串 "OPEN" 或 "CLOSE"
*/
export const apiOperateLockWithUser = async (payload) => {
  const { lockId, status, transmissionType = 0 } = payload;
  const operateRes = await request('/device/lock/operateLockWithUser', 'POST', {
    lockId,
    status,
    transmissionType
  });
  return operateRes.data;
};
